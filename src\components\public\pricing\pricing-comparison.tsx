"use client";

import { Check, X, Search, Star, Crown, Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import type { PlatformPackages } from "@/types/database/tables";
import { useState, useMemo, useEffect } from "react";
import Link from "next/link";

type PricingComparisonProps = {
  packages: PlatformPackages[];
};

export function PricingComparison({ packages }: PricingComparisonProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const formatFeatures = (features: unknown): string[] => {
    if (Array.isArray(features)) {
      return features.filter((item): item is string => typeof item === "string");
    }
    if (typeof features === "string") {
      try {
        const parsed = JSON.parse(features);
        return formatFeatures(parsed);
      } catch {
        return [];
      }
    }
    if (typeof features === "object" && features !== null) {
      const obj = features as Record<string, unknown>;
      if (Array.isArray(obj.perks)) {
        return (obj.perks as unknown[]).filter((v): v is string => typeof v === "string");
      }
      const directStrings = Object.values(obj).filter((v): v is string => typeof v === "string");
      if (directStrings.length) return directStrings;
      const nested = Object.values(obj)
        .flatMap((v) => (Array.isArray(v) ? v : []))
        .filter((v): v is string => typeof v === "string");
      return nested;
    }
    return [];
  };

  const prettifyFeature = (text: string): string => {
    try {
      let t = (text || "").toString().trim();
      if (!t) return t;
      t = t.replace(/^Maks\s/i, "Maksimum ");
      t = t.replace(/\bmax\b/i, "Maksimum");
      t = t.replace(/uye/gi, "üye");
      t = t.charAt(0).toUpperCase() + t.slice(1);
      return t;
    } catch {
      return text as string;
    }
  };

  const pickDisplayPackage = (tier: string): PlatformPackages | undefined => {
    const byTier = packages.filter((p) => p.tier === tier);
    const yearly = byTier.find((p) => p.duration === "yearly");
    const lifetime = byTier.find((p) => !p.duration || p.duration === "lifetime");
    if (tier === "free") return lifetime ?? yearly ?? byTier[0];
    return yearly ?? lifetime ?? byTier[0];
  };

  const starter = pickDisplayPackage("starter");
  const pro = pickDisplayPackage("professional");
  const free = pickDisplayPackage("free");

  const tl = (n: number) => n.toLocaleString("tr-TR");

  const starterFeatures = formatFeatures(starter?.features ?? []);
  const proFeatures = formatFeatures(pro?.features ?? []);
  const freeFeatures = formatFeatures(free?.features ?? []);
  
  const allFeatures = useMemo(() => {
    return Array.from(new Set([
      ...starterFeatures,
      ...proFeatures,
      ...freeFeatures,
    ])).sort((a, b) => a.localeCompare(b, "tr"));
  }, [starterFeatures, proFeatures, freeFeatures]);

  const filteredFeatures = useMemo(() => {
    return allFeatures.filter(feature => 
      searchTerm === "" || feature.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [allFeatures, searchTerm]);

  const priceDisplay = (pkg?: PlatformPackages) => {
    if (!pkg) return { price: "-", suffix: "" };
    const price = typeof (pkg as any).price === "number" ? (pkg as any).price : Number((pkg as any).price);
    const suffix = pkg.duration === "yearly" ? "/yıl" : "ömür boyu";
    return { price: `₺${tl(price)}`, suffix };
  };

  const starterPrice = priceDisplay(starter);
  const proPrice = priceDisplay(pro);
  const freePrice = priceDisplay(free);

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-b from-muted/30 to-background">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="inline-flex items-center gap-2 rounded-full border border-primary/30 bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6">
              <Check className="h-4 w-4" />
              <span>Özellik Karşılaştırması</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Hangi Plan <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-600">Size Uygun?</span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Detaylı özellik karşılaştırması ile ihtiyaçlarınıza en uygun planı keşfedin.
            </p>
          </div>

          {/* Search */}
          <div className={`mb-12 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="bg-card/60 backdrop-blur-sm border border-border/20 rounded-2xl p-6 max-w-md mx-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder="Özellik ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-card/80 border-border/30"
                />
              </div>
            </div>
          </div>

          {/* Comparison Table */}
          <div className={`transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="bg-card/80 backdrop-blur-sm border border-border/20 rounded-2xl overflow-hidden shadow-xl">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gradient-to-r from-primary/5 to-primary/10 border-b border-border/20">
                      <th className="text-left p-6 font-semibold text-foreground min-w-[300px]">
                        Özellikler ({filteredFeatures.length})
                      </th>
                      <th className="text-center p-6 font-semibold text-foreground min-w-[200px]">
                        <div className="space-y-3">
                          <div className="flex items-center justify-center gap-2">
                            <Users className="h-5 w-5 text-gray-500" />
                            <span className="text-lg">{free?.name ?? "Başlangıç"}</span>
                          </div>
                          <div className="text-3xl font-bold text-primary">{freePrice.price}</div>
                          <div className="text-sm text-muted-foreground">{freePrice.suffix}</div>
                        </div>
                      </th>
                      <th className="text-center p-6 font-semibold text-foreground bg-gradient-to-b from-primary/10 to-blue-600/10 min-w-[200px] relative">
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-gradient-to-r from-primary to-blue-600 text-white">
                            <Star className="h-3 w-3 mr-1 fill-current" />
                            En Popüler
                          </Badge>
                        </div>
                        <div className="space-y-3 pt-2">
                          <div className="flex items-center justify-center gap-2">
                            <Star className="h-5 w-5 text-blue-500" />
                            <span className="text-lg">{starter?.name ?? "Gelişim"}</span>
                          </div>
                          <div className="text-3xl font-bold text-primary">{starterPrice.price}</div>
                          <div className="text-sm text-muted-foreground">{starterPrice.suffix}</div>
                        </div>
                      </th>
                      <th className="text-center p-6 font-semibold text-foreground min-w-[200px]">
                        <div className="space-y-3">
                          <div className="flex items-center justify-center gap-2">
                            <Crown className="h-5 w-5 text-purple-500" />
                            <span className="text-lg">{pro?.name ?? "Kurumsal"}</span>
                          </div>
                          <div className="text-3xl font-bold text-primary">{proPrice.price}</div>
                          <div className="text-sm text-muted-foreground">{proPrice.suffix}</div>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredFeatures.length === 0 ? (
                      <tr>
                        <td colSpan={4} className="p-12 text-center text-muted-foreground">
                          <div className="space-y-2">
                            <Search className="h-8 w-8 mx-auto text-muted-foreground/50" />
                            <div>Aradığınız kriterlere uygun özellik bulunamadı.</div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSearchTerm("")}
                            >
                              Aramayı Temizle
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      filteredFeatures.map((feature, idx) => {
                        const hasStarter = starterFeatures.includes(feature);
                        const hasPro = proFeatures.includes(feature);
                        const hasFree = freeFeatures.includes(feature);
                        
                        return (
                          <tr key={idx} className="border-b border-white/10 hover:bg-white/20 transition-colors group">
                            <td className="p-4 text-foreground font-medium">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-primary/30 rounded-full group-hover:bg-primary transition-colors"></div>
                                {prettifyFeature(feature)}
                              </div>
                            </td>
                            <td className="p-4 text-center">
                              {hasFree ? (
                                <div className="inline-flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                                  <Check className="h-5 w-5 text-green-600" />
                                </div>
                              ) : (
                                <div className="inline-flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                                  <X className="h-5 w-5 text-red-600" />
                                </div>
                              )}
                            </td>
                            <td className="p-4 text-center bg-gradient-to-b from-primary/5 to-blue-600/5">
                              {hasStarter ? (
                                <div className="inline-flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                                  <Check className="h-5 w-5 text-green-600" />
                                </div>
                              ) : (
                                <div className="inline-flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                                  <X className="h-5 w-5 text-red-600" />
                                </div>
                              )}
                            </td>
                            <td className="p-4 text-center">
                              {hasPro ? (
                                <div className="inline-flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                                  <Check className="h-5 w-5 text-green-600" />
                                </div>
                              ) : (
                                <div className="inline-flex items-center justify-center w-8 h-8 bg-red-100 rounded-full">
                                  <X className="h-5 w-5 text-red-600" />
                                </div>
                              )}
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className={`mt-12 text-center transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="bg-white/60 backdrop-blur-sm border border-white/20 rounded-2xl p-6 max-w-4xl mx-auto">
              <p className="text-sm text-muted-foreground mb-4">
                <strong className="text-foreground">* Tüm planlar 14 gün ücretsiz deneme ile başlar.</strong>
                {" "}İstediğiniz zaman iptal edebilirsiniz. Kredi kartı bilgisi gerekmez.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild variant="outline">
                  <Link href="/contact">
                    Daha fazla bilgi için iletişim
                  </Link>
                </Button>
                <Button asChild className="bg-gradient-to-r from-primary to-blue-600 text-white">
                  <Link href="/auth/register">
                    Hemen ücretsiz başla
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
