/**
 * TypeScript types for gym rating functionality
 * Based on the salon rating feature design document
 */

export interface UserRating {
  id: string;
  gym_id: string;
  profile_id: string;
  rating: number; // 1-5
  comment: string | null;
  created_at: string;
  updated_at: string;
}

export interface RatingFormData {
  rating: number;
  comment?: string;
}

export interface GymRatingStats {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: Record<string, number>;
}

export interface CreateRatingPayload {
  gymId: string;
  rating: number; // 1-5
  comment?: string;
}

export interface UpdateRatingPayload {
  ratingId: string;
  rating: number;
  comment?: string;
}

export interface DeleteRatingPayload {
  ratingId: string;
}

export interface UserRatingResponse {
  success: boolean;
  data?: UserRating;
  error?: string;
}

export interface CreateRatingResponse {
  success: boolean;
  data?: UserRating;
  error?: string;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// Component prop interfaces
export interface StarRatingProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  readonly?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export interface RatingDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  gymId: string;
  onRatingSubmit: (rating: UserRating) => void;
  existingRating?: UserRating;
}

export interface UserRatingSectionProps {
  gymId: string;
  userId?: string;
  membershipStatus: 'none' | 'pending' | 'active' | 'suspended';
  onRatingUpdate?: () => void;
}

export interface ExistingRatingCardProps {
  rating: UserRating;
  onEdit: () => void;
  onDelete: () => void;
}

// Rating validation constants
export const RATING_CONSTRAINTS = {
  MIN_RATING: 1,
  MAX_RATING: 5,
  MAX_COMMENT_LENGTH: 500,
  MIN_COMMENT_LENGTH: 0, // Comments are optional
} as const;

// Rating labels for UI
export const RATING_LABELS = {
  1: 'Çok Kötü',
  2: 'Kötü',
  3: 'Orta',
  4: 'İyi',
  5: 'Mükemmel',
} as const;

// Rating states
export type RatingSubmissionState = 'idle' | 'submitting' | 'success' | 'error';

export interface RatingSubmissionStatus {
  state: RatingSubmissionState;
  message?: string;
}