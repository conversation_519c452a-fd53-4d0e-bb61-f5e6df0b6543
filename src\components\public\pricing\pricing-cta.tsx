"use client";

import { useState, useEffect } from "react";
import { Check, Phone, Clock, Shield, Users, Crown, ArrowRight, Sparkles } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

export function PricingCTA() {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    
    // Set a countdown timer for urgency (e.g., special offer ends in 7 days)
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + 7);
    
    const updateTimer = () => {
      const now = new Date().getTime();
      const target = targetDate.getTime();
      const difference = target - now;
      
      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000)
        });
      }
    };
    
    updateTimer();
    const interval = setInterval(updateTimer, 1000);
    
    return () => clearInterval(interval);
  }, []);

  const benefits = [
    { icon: Shield, text: "Güvenli kurulum", desc: "SSL korumalı" },
    { icon: Clock, text: "24 saat içinde", desc: "Hızlı başlangıç" },
    { icon: Users, text: "7/24 Türkçe destek", desc: "Her zaman yanınızda" },
    { icon: Crown, text: "Premium özellikler", desc: "14 gün ücretsiz" }
  ];

  const urgencyFeatures = [
    "✨ İlk 100 kayıt eden salona özel %20 ek indirim",
    "🎯 Ücretsiz veri transferi ve kurulum desteği",
    "🚀 3 aylık premium e-mail pazarlama ücretsiz",
    "💎 İlk yıl için özel müşteri başarı uzmanı"
  ];

  const stats = [
    { number: "500+", label: "Salon", subLabel: "aktif kullanıcı" },
    { number: "99.9%", label: "Uptime", subLabel: "güvenilirlik" },
    { number: "<1dk", label: "Yanıt", subLabel: "destek süresi" },
    { number: "4.9/5", label: "Puan", subLabel: "memnuniyet" }
  ];

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-primary/10 via-background to-blue-600/10 relative overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Urgency Timer */}
          <div className={`text-center mb-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Badge className="bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 text-sm font-medium mb-6 animate-pulse">
              <Clock className="h-4 w-4 mr-2" />
              ⚡ Sınırlı Süre Teklifi
            </Badge>
            
            <div className="bg-white/80 backdrop-blur-sm border border-white/20 rounded-2xl p-6 max-w-md mx-auto mb-6">
              <h3 className="text-lg font-bold text-foreground mb-4">Özel Fırsat Bitiyor!</h3>
              <div className="grid grid-cols-4 gap-3 text-center">
                <div className="bg-primary/10 rounded-lg p-3">
                  <div className="text-2xl font-bold text-primary">{timeLeft.days}</div>
                  <div className="text-xs text-muted-foreground">Gün</div>
                </div>
                <div className="bg-primary/10 rounded-lg p-3">
                  <div className="text-2xl font-bold text-primary">{timeLeft.hours}</div>
                  <div className="text-xs text-muted-foreground">Saat</div>
                </div>
                <div className="bg-primary/10 rounded-lg p-3">
                  <div className="text-2xl font-bold text-primary">{timeLeft.minutes}</div>
                  <div className="text-xs text-muted-foreground">Dakika</div>
                </div>
                <div className="bg-primary/10 rounded-lg p-3">
                  <div className="text-2xl font-bold text-primary">{timeLeft.seconds}</div>
                  <div className="text-xs text-muted-foreground">Saniye</div>
                </div>
              </div>
            </div>
          </div>

          {/* Main CTA Card */}
          <div className={`transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Card className="bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-sm border border-white/20 overflow-hidden relative">
              <CardContent className="p-8 md:p-12">
                <div className="text-center mb-12">
                  <div className="inline-flex items-center gap-2 rounded-full border border-primary/30 bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6">
                    <Sparkles className="h-4 w-4" />
                    <span>Hemen Başlamaya Hazır mısınız?</span>
                  </div>
                  
                  <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-600">14 Gün Ücretsiz</span>
                    <br />Denemeye Başlayın
                  </h2>
                  
                  <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto leading-relaxed">
                    Kredi kartı bilgisi gerektirmeden tüm premium özellikleri deneyimleyin.
                    Salonunuzun dönüşümünü bugün başlatın.
                  </p>

                  {/* Urgency Features */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 mb-8">
                    <h3 className="text-lg font-bold text-green-800 mb-4 flex items-center justify-center gap-2">
                      <Crown className="h-5 w-5" />
                      Bu Hafta Başlayanlar İçin Özel Avantajlar
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-left">
                      {urgencyFeatures.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-green-700">
                          <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                    <Button asChild size="lg" className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all text-lg px-8 py-4">
                      <Link href="/auth/register">
                        <Crown className="mr-2 h-5 w-5" />
                        Ücretsiz Başla - 14 Gün
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Link>
                    </Button>
                    <Button asChild size="lg" variant="outline" className="bg-white/70 border-white/30 hover:bg-white/90 text-lg px-8 py-4">
                      <Link href="#pricing-plans">
                        <Phone className="mr-2 h-4 w-4" />
                        Demo Talep Et
                      </Link>
                    </Button>
                  </div>

                  {/* Trust Indicators */}
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground mb-6">
                      <strong className="text-foreground">Kredi kartı gerekmez.</strong>
                      {" "}14 gün sonunda otomatik ücretlendirme yapılmaz. İstediğiniz zaman iptal edebilirsiniz.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Stats Grid */}
          <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 mt-16 transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <Card className="bg-white/60 backdrop-blur-sm border-white/20 hover:bg-white/70 transition-all hover:scale-105">
                  <CardContent className="p-6">
                    <div className="text-3xl font-bold text-primary mb-1">{stat.number}</div>
                    <div className="font-semibold text-foreground text-sm">{stat.label}</div>
                    <div className="text-xs text-muted-foreground">{stat.subLabel}</div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>

          {/* Benefits Grid */}
          <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              return (
                <div key={index} className="text-center group">
                  <Card className="bg-white/60 backdrop-blur-sm border-white/20 hover:bg-white/70 transition-all group-hover:scale-105">
                    <CardContent className="p-6">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="font-semibold text-foreground mb-2">{benefit.text}</h3>
                      <p className="text-sm text-muted-foreground">{benefit.desc}</p>
                    </CardContent>
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Final Note */}
          <div className={`mt-16 text-center transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="bg-white/60 backdrop-blur-sm border border-white/20 rounded-2xl p-6 max-w-4xl mx-auto">
              <p className="text-sm text-muted-foreground">
                <strong className="text-foreground">500+ spor salonu</strong> tarafından güveniliyor.
                {" "}<strong className="text-foreground">99.9% uptime</strong> garantisi ile kesintisiz hizmet.
                {" "}<strong className="text-foreground">KVKK uyumlu</strong> güvenli altyapı.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/10 to-primary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        {/* Floating elements */}
        <div className="absolute top-20 right-20 w-4 h-4 bg-primary/20 rounded-full animate-float"></div>
        <div className="absolute bottom-32 left-16 w-6 h-6 bg-blue-500/20 rounded-full animate-float delay-500"></div>
        <div className="absolute top-1/3 right-1/3 w-8 h-8 bg-primary/10 rounded-full animate-float delay-1000"></div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </section>
  );
}
