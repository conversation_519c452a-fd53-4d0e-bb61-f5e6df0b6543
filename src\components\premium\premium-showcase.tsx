import { <PERSON><PERSON><PERSON>, <PERSON>ge, Building2, CalendarRange, CheckCircle2, LineChart, Shield, Sparkles, Target, Users2, Zap } from "lucide-react";
import { AnimatedSection } from "../ui/animated-section";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";

export function PremiumShowcase() {
  const features = [
    {
      icon: CalendarRange,
      title: 'Ak<PERSON>llı Randevu Yönetimi',
      description: 'Çakışma önleme, bekleme listesi, eğitmen/oda ataması ve otomatik hatırlatmalar ile randevu süreçlerinizi otomatikleştirin.',
      features: [
        'Çak<PERSON>şma tespiti ve önleme',
        'Oda ve ekipman otomatik ataması',
        'SMS/WhatsApp/Email hatırlatmalar',
        'Bekleme listesi yönetimi',
        'Grup dersleri ve etkinlik planlama'
      ],
      color: 'from-blue-500/10 via-blue-500/5 to-transparent',
      borderColor: 'border-blue-200/30',
      iconBg: 'bg-blue-500/10',
      iconColor: 'text-blue-600',
      tag: 'Otom<PERSON>yon'
    },
    {
      icon: Users2,
      title: '<PERSON>yelik ve CRM',
      description: 'Üyelik döngüsü, paketler ve iletişim akışlarını tek panelden yönetin. Müşteri ilişkilerinizi güçlendirin.',
      features: [
        'Kapsamlı üye profilleri ve geçmişi',
        'Paket yönetimi ve otomatik yenileme',
        'Segment ve etiketleme sistemi',
        'Kişiselleştirilmiş iletişim',
        'Sadakat programları yönetimi'
      ],
      color: 'from-green-500/10 via-green-500/5 to-transparent',
      borderColor: 'border-green-200/30',
      iconBg: 'bg-green-500/10',
      iconColor: 'text-green-600',
      tag: 'CRM'
    },
    {
      icon: LineChart,
      title: 'Gelişmiş Analitik',
      description: 'Gelir, doluluk, randevu ve antrenör performans raporları ile veri odaklı kararlar alın ve işletmenizi optimize edin.',
      features: [
        'Canlı KPI panoları ve metrikler',
        'Periyot karşılaştırma ve trend analizi',
        'Gelişmiş raporlama ve dışa aktarma',
        'Tahminleme ve projeksiyon araçları',
        'Özel rapor oluşturma'
      ],
      color: 'from-purple-500/10 via-purple-500/5 to-transparent',
      borderColor: 'border-purple-200/30',
      iconBg: 'bg-purple-500/10',
      iconColor: 'text-purple-600',
      tag: 'Analitik'
    },
    {
      icon: Building2,
      title: 'Çoklu Şube Yönetimi',
      description: 'Merkezden tüm şubeleri yönetin, yetkilendirme ve raporlamayı konsolide edin. Ölçeklenebilir büyüme için tasarlandı.',
      features: [
        'Merkezi rol ve yetki yönetimi',
        'Şube bazında raporlama ve analiz',
        'Konsolide finansal görünürlük',
        'Inter-şube transfer ve koordinasyon',
        'Marka tutarlılığı yönetimi'
      ],
      color: 'from-orange-500/10 via-orange-500/5 to-transparent',
      borderColor: 'border-orange-200/30',
      iconBg: 'bg-orange-500/10',
      iconColor: 'text-orange-600',
      tag: 'Ölçeklendirme'
    },
  ];

  const additionalFeatures = [
    {
      icon: Shield,
      title: 'Kurumsal Güvenlik',
      description: 'KVKK uyumlu, SSL şifreleme ve çok katmanlı güvenlik'
    },
    {
      icon: Zap,
      title: 'Hızlı Performans',
      description: 'Bulut tabanlı altyapı ile anlık yanıt süreleri'
    },
    {
      icon: Target,
      title: 'Hedef Odaklı',
      description: 'Kişiselleştirilmiş hedefler ve başarı takibi'
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-background">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <AnimatedSection animation="fade-up" delay={100}>
          <div className="text-center mb-16">
            <Badge  className="mb-4 px-4 py-2 border-primary/20 bg-primary/10">
              <Sparkles className="mr-2 h-4 w-4" />
              Platform Özellikleri
            </Badge>
            
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                Güçlü Özellikler
              </span>
              <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Akıllı Çözümler
              </span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Spor salonu yönetiminizin her alanında size yardımcı olacak ileri teknoloji özellikleri keşfedin.
            </p>
          </div>
        </AnimatedSection>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <AnimatedSection
                key={feature.title}
                animation="fade-up"
                delay={200 + (index * 150)}
              >
                <div className={`group relative overflow-hidden rounded-2xl border ${feature.borderColor} bg-gradient-to-br ${feature.color} p-8 backdrop-blur transition-all duration-500 hover:border-primary/40 hover:-translate-y-2 hover:shadow-2xl`}>
                  {/* Background decorations */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
                  <div className="absolute top-0 right-0 h-32 w-32 rounded-full bg-gradient-to-br from-background/10 to-transparent blur-2xl" />
                  
                  <div className="relative">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className={`${feature.iconBg} rounded-xl p-3 transition-transform duration-300 group-hover:scale-110`}>
                          <IconComponent className={`h-8 w-8 ${feature.iconColor}`} />
                        </div>
                        <div>
                          <Badge className="mb-2 text-xs">
                            {feature.tag}
                          </Badge>
                          <h3 className="text-xl font-bold text-foreground">
                            {feature.title}
                          </h3>
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      {feature.description}
                    </p>

                    {/* Features list */}
                    <div className="space-y-3">
                      {feature.features.map((item, idx) => (
                        <div key={idx} className="flex items-start gap-3">
                          <CheckCircle2 className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-foreground leading-relaxed">
                            {item}
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* Learn more link */}
                    <div className="mt-6 pt-4 border-t border-border/50">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="group/btn p-0 h-auto font-medium text-primary hover:bg-transparent"
                        asChild
                      >
                        <Link href="/features">
                          Daha fazla öğren
                          <ArrowRight className="ml-1 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </AnimatedSection>
            );
          })}
        </div>

        {/* Additional Features */}
        <AnimatedSection animation="fade-up" delay={800}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {additionalFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div
                  key={index}
                  className="text-center p-6 rounded-xl border border-border/50 bg-card/50 backdrop-blur transition-all duration-300 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1"
                >
                  <div className="mx-auto mb-4 w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center">
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                  
                  <h4 className="text-lg font-semibold text-foreground mb-2">
                    {feature.title}
                  </h4>
                  
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </AnimatedSection>

        {/* CTA Section */}
        <AnimatedSection animation="fade-up" delay={1000}>
          <div className="text-center mt-16">
            <div className="inline-flex flex-col sm:flex-row items-center gap-4">
              <Button
                asChild
                size="lg"
                className="group h-12 px-8 text-base font-semibold shadow-lg transition-all duration-300 hover:shadow-xl"
              >
                <Link href="/features">
                  Tüm özellikleri keşfedin
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              
              <Button
                asChild
                variant="outline"
                size="lg"
                className="h-12 px-8 text-base font-semibold border-primary/20 hover:bg-primary/5"
              >
                <Link href="/onboarding">
                  Hemen başla
                </Link>
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground mt-4">
              İlk Ay ücretsiz deneme • Kredi kartı gerekmez
            </p>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}