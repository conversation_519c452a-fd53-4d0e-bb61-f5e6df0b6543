"use client";

import { useState, useEffect, useMemo } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Calculator, TrendingUp, Clock, Users, DollarSign, ArrowRight, Sparkles } from "lucide-react";
import Link from "next/link";

export function PricingROICalculator() {
  const [memberCount, setMemberCount] = useState([200]);
  const [monthlyFee, setMonthlyFee] = useState([150]);
  const [timeSpent, setTimeSpent] = useState([20]);
  const [isVisible, setIsVisible] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState("starter");

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const plans = useMemo(() => ({
    starter: { name: "<PERSON><PERSON><PERSON><PERSON>", price: 599, maxMembers: 500 },
    professional: { name: "<PERSON><PERSON><PERSON>", price: 999, maxMembers: null }
  }), []);

  const roiCalculations = useMemo(() => {
    const members = memberCount[0];
    const avgFee = monthlyFee[0];
    const hoursPerWeek = timeSpent[0];
    const planPrice = plans[selectedPlan as keyof typeof plans].price;
    
    const monthlyRevenue = members * avgFee;
    const yearlyRevenue = monthlyRevenue * 12;
    
    const hoursPerMonth = hoursPerWeek * 4;
    const hourlyRate = 50;
    const timeSavingsValue = hoursPerMonth * hourlyRate * 12;
    
    const memberRetentionImprovement = 0.15;
    const revenueFromRetention = yearlyRevenue * memberRetentionImprovement;
    
    const newMemberAcquisition = 0.10;
    const revenueFromNewMembers = yearlyRevenue * newMemberAcquisition;
    
    const totalYearlyBenefits = timeSavingsValue + revenueFromRetention + revenueFromNewMembers;
    const totalCost = planPrice;
    const netBenefit = totalYearlyBenefits - totalCost;
    const roiPercentage = ((netBenefit / totalCost) * 100);
    const paybackPeriod = totalCost / (totalYearlyBenefits / 12);
    
    return {
      monthlyRevenue,
      yearlyRevenue,
      timeSavingsValue,
      revenueFromRetention,
      revenueFromNewMembers,
      totalYearlyBenefits,
      totalCost,
      netBenefit,
      roiPercentage,
      paybackPeriod,
      hoursPerMonth
    };
  }, [memberCount, monthlyFee, timeSpent, selectedPlan, plans]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('tr-TR').format(Math.round(num));
  };

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-b from-background to-primary/5">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="inline-flex items-center gap-2 rounded-full border border-primary/30 bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6">
              <Calculator className="h-4 w-4" />
              <span>ROI Hesaplayicisi</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              Sportiva ile <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-600">Ne Kadar Kazanirsininiz?</span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Salonunuzun buyuklugunee gore potansiyel tasarruf ve gelir artisinizi hesaplayin.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div className={`space-y-8 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'}`}>
              <Card className="bg-white/60 backdrop-blur-sm border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    Salon Bilgileriniz
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Aktif Uye Sayisi: <span className="text-primary font-bold">{formatNumber(memberCount[0])}</span>
                    </Label>
                    <Slider
                      value={memberCount}
                      onValueChange={setMemberCount}
                      max={1000}
                      min={50}
                      step={25}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>50</span>
                      <span>1000</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Ortalama Aylik Ucret: <span className="text-primary font-bold">{formatCurrency(monthlyFee[0])}</span>
                    </Label>
                    <Slider
                      value={monthlyFee}
                      onValueChange={setMonthlyFee}
                      max={500}
                      min={100}
                      step={25}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>100 TL</span>
                      <span>500 TL</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Haftalik Yonetim Saati: <span className="text-primary font-bold">{formatNumber(timeSpent[0])} saat</span>
                    </Label>
                    <Slider
                      value={timeSpent}
                      onValueChange={setTimeSpent}
                      max={40}
                      min={5}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>5 saat</span>
                      <span>40 saat</span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <Label className="text-sm font-medium">Plan Secimi</Label>
                    <div className="grid grid-cols-2 gap-3">
                      {Object.entries(plans).map(([key, plan]) => (
                        <Button
                          key={key}
                          variant={selectedPlan === key ? "default" : "outline"}
                          onClick={() => setSelectedPlan(key)}
                          className={`p-4 h-auto ${selectedPlan === key ? 'bg-primary text-primary-foreground' : 'bg-white/80'}`}
                        >
                          <div className="text-center">
                            <div className="font-semibold">{plan.name}</div>
                            <div className="text-sm opacity-80">{formatCurrency(plan.price)}/yil</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className={`space-y-6 transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4'}`}>
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-700">{Math.round(roiCalculations.roiPercentage)}%</div>
                    <div className="text-sm text-green-600">ROI Orani</div>
                  </CardContent>
                </Card>
                <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-700">{Math.round(roiCalculations.paybackPeriod)}</div>
                    <div className="text-sm text-blue-600">Geri Odeme (Ay)</div>
                  </CardContent>
                </Card>
              </div>

              <Card className="bg-white/80 backdrop-blur-sm border-white/20">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <TrendingUp className="h-5 w-5 text-primary" />
                    Yillik Faydalar
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Zaman Tasarrufu Degeri</span>
                      <span className="font-semibold text-green-600">{formatCurrency(roiCalculations.timeSavingsValue)}</span>
                    </div>
                    <div className="text-xs text-muted-foreground ml-0">
                      Ayda {roiCalculations.hoursPerMonth} saat x 50 TL/saat x 12 ay
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Uye Bagliligi Artisi</span>
                      <span className="font-semibold text-green-600">{formatCurrency(roiCalculations.revenueFromRetention)}</span>
                    </div>
                    <div className="text-xs text-muted-foreground ml-0">
                      %15 retention artisi
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Yeni Uye Kazanimi</span>
                      <span className="font-semibold text-green-600">{formatCurrency(roiCalculations.revenueFromNewMembers)}</span>
                    </div>
                    <div className="text-xs text-muted-foreground ml-0">
                      %10 yeni uye artisi
                    </div>
                  </div>

                  <hr className="border-border" />
                  
                  <div className="flex justify-between items-center text-lg font-bold">
                    <span>Toplam Yillik Fayda</span>
                    <span className="text-green-600">{formatCurrency(roiCalculations.totalYearlyBenefits)}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-muted-foreground">Yillik Maliyet</span>
                    <span className="text-red-600">-{formatCurrency(roiCalculations.totalCost)}</span>
                  </div>
                  
                  <hr className="border-border" />
                  
                  <div className="flex justify-between items-center text-xl font-bold">
                    <span>Net Kazanc</span>
                    <span className="text-primary">{formatCurrency(roiCalculations.netBenefit)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-r from-primary to-blue-600 text-white border-0">
                <CardContent className="p-6 text-center">
                  <h3 className="text-xl font-bold mb-2">Hesaplamalar Cok Iyi Gorunuyor!</h3>
                  <p className="text-white/90 mb-4">14 gunluk ucretsiz deneme ile hemen baslayin</p>
                  <Button asChild size="lg" className="bg-white text-primary hover:bg-white/90">
                    <Link href="/auth/register">
                      Ucretsiz Basla
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                  <div className="mt-3 text-sm text-white/80">
                    Kredi karti gerekmez • Aninda kurulum
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className={`mt-16 text-center transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="bg-white/60 backdrop-blur-sm border border-white/20 rounded-2xl p-6 max-w-4xl mx-auto">
              <p className="text-sm text-muted-foreground">
                <strong className="text-foreground">* Bu hesaplamalar tahmini degerlerdir.</strong>
                {" "}Gercek sonuclar salon buyuklugu, konum ve yonetim verimliligine gore degisebilir.
                Sportiva nin sagladigi zaman tasarrufu ve uye memnuniyeti artisi gercek musteri deneyimlerine dayanmaktadir.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}