"use client";

import { useState, useMemo, useEffect } from "react";
import { ChevronDown, Search, HelpCircle, CreditCard, Shield, Settings, Headphones, Zap } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";

export function PricingFAQ() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [openItems, setOpenItems] = useState<Set<number>>(new Set([0]));
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const categories = [
    { id: "all", label: "Tümü", icon: HelpCircle, color: "primary" },
    { id: "pricing", label: "<PERSON>yatlandırma", icon: CreditCard, color: "blue" },
    { id: "features", label: "<PERSON><PERSON><PERSON><PERSON>", icon: Zap, color: "green" },
    { id: "security", label: "Güvenlik", icon: Shield, color: "red" },
    { id: "setup", label: "Kurulum", icon: Settings, color: "purple" },
    { id: "support", label: "Destek", icon: Headphones, color: "orange" }
  ];

  const filteredFAQs = useMemo(() => {
    const faqs = [
      {
        category: "pricing",
        question: "Ücretsiz deneme süresi nasıl çalışır?",
        answer: "14 gün boyunca tüm Pro özelliklerini ücretsiz deneyebilirsiniz. Kredi kartı bilgisi gerekmez. Deneme süresi sonunda otomatik ücretlendirme yapılmaz. Sadece devam etmek istediğinizde plan seçimi yaparsınız.",
        popular: true
      },
      {
        category: "pricing",
        question: "Plan değişikliği yapabilir miyim?",
        answer: "Evet, istediğiniz zaman planınızı yükseltebilir veya düşürebilirsiniz. Değişiklik hemen geçerli olur ve ücretlendirme pro-rata olarak hesaplanır. Plan düşürme işleminde mevcut dönem sonuna kadar üst plan özelliklerini kullanmaya devam edersiniz."
      },
      {
        category: "pricing",
        question: "İptal politikanız nedir?",
        answer: "İstediğiniz zaman aboneliğinizi iptal edebilirsiniz. İptal işlemi sonrası mevcut dönem sonuna kadar hizmet almaya devam edersiniz. Veri exportu için 30 gün süre tanıyoruz."
      },
      {
        category: "pricing",
        question: "Fatura ve ödeme seçenekleri nelerdir?",
        answer: "Kredi kartı, banka kartı ve havale ile ödeme yapabilirsiniz. Aylık veya yıllık ödeme seçenekleri mevcuttur. Yıllık ödemede %65 indirim uygulanır. Tüm ödemeler SSL şifreleme ile güvence altındadır."
      },
      {
        category: "features",
        question: "Hangi özellikler hangi planlarda var?",
        answer: "Başlangıç planında temel üye yönetimi ve randevu sistemi var. Gelişim planında gelişmiş raporlama, SMS entegrasyonu ve API erişimi eklenir. Kurumsal planda ise çoklu şube yönetimi ve özel entegrasyonlar bulunur."
      },
      {
        category: "features",
        question: "Mobil uygulama dahil mi?",
        answer: "Evet, hem salon yöneticileri hem de üyeler için iOS ve Android uygulamalarımız tüm planlarımızda dahildir. Uygulamalar ücretsizdir ve sürekli güncellenir."
      },
      {
        category: "features",
        question: "Veri limiti var mı?",
        answer: "Hayır, veri depolama konusunda herhangi bir limitimiz yoktur. İstediğiniz kadar üye, randevu ve rapor verisi saklayabilirsiniz. Yedekleme işlemleri otomatik olarak gerçekleşir."
      },
      {
        category: "security",
        question: "Veri güvenliği nasıl sağlanıyor?",
        answer: "Tüm verileriniz SSL şifreleme ile korunur. Düzenli yedeklemeler alınır ve KVKK uyumlu veri işleme politikalarımız vardır. Sunucularımız Türkiye'de bulunur ve 7/24 izlenir."
      },
      {
        category: "security",
        question: "KVKK uyumluluğu nasıl sağlanıyor?",
        answer: "Platformumuz KVKK gereksinimlerine tam uyumludur. Veri işleme süreçleri şeffaftır, kullanıcı onayları alınır ve veri taşınabilirliği sağlanır. Düzenli KVKK denetimlerinden geçmekteyiz."
      },
      {
        category: "setup",
        question: "Kurulum süreci ne kadar sürer?",
        answer: "Standart kurulum 24 saat içinde tamamlanır. Uzman ekibimiz salonunuzu sisteme entegre etmenize yardımcı olur. Veri aktarımı varsa bu süre 2-3 güne çıkabilir.",
        popular: true
      },
      {
        category: "setup",
        question: "Mevcut verilerimi aktarabilir miyim?",
        answer: "Evet, mevcut üye verilerinizi, randevu geçmişinizi ve diğer salon verilerinizi sisteme aktarabiliriz. Bu işlem ücretsizdir ve veri bütünlüğü garanti edilir."
      },
      {
        category: "setup",
        question: "Eğitim desteği veriliyor mu?",
        answer: "Evet, kurulum sonrası ekibiniz için kapsamlı eğitim programı düzenliyoruz. Video eğitimler, canlı webinarlar ve birebir eğitim seçenekleri mevcuttur."
      },
      {
        category: "support",
        question: "Destek hizmetleri nasıl?",
        answer: "7/24 Türkçe destek hizmeti sunuyoruz. Telefon, email, canlı chat ve uzaktan bağlantı seçenekleri mevcuttur. Kritik sorunlarda 1 saat içinde yanıt garantisi veriyoruz.",
        popular: true
      },
      {
        category: "support",
        question: "Teknik sorunlarda ne yapmalıyım?",
        answer: "Destek ekibimize 7/24 ulaşabilirsiniz. Acil durumlar için telefon hattımız her zaman açıktır. Ayrıca sistem üzerinden ticket açabilir ve ilerleyişi takip edebilirsiniz."
      }
    ];

    return faqs.filter(faq => {
      const matchesSearch = searchTerm === "" || 
        faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === "all" || faq.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [searchTerm, selectedCategory]);

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index);
    } else {
      newOpenItems.add(index);
    }
    setOpenItems(newOpenItems);
  };

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-b from-background to-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="inline-flex items-center gap-2 rounded-full border border-primary/30 bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6">
              <HelpCircle className="h-4 w-4" />
              <span>Sıkça Sorulan Sorular</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-600">Merak Ettikleriniz</span> Burada
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Fiyatlandırma ve hizmetlerimiz hakkında en çok sorulan soruların yanıtları.
            </p>
          </div>

          {/* Search and Filter */}
          <div className={`mb-12 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Card className="bg-card/60 backdrop-blur-sm border-border/20">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  {/* Search */}
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="text"
                      placeholder="Soru ara..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 bg-card/80 border-border/30"
                    />
                  </div>
                  
                  {/* Categories */}
                  <div className="flex flex-wrap gap-2">
                    {categories.map(({ id, label, icon: Icon }) => (
                      <Button
                        key={id}
                        variant={selectedCategory === id ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedCategory(id)}
                        className={`${selectedCategory === id ? 'bg-primary text-primary-foreground' : 'bg-card/80 border-border/30'} hover:scale-105 transition-all`}
                      >
                        <Icon className="h-3 w-3 mr-1" />
                        {label}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {filteredFAQs.length > 0 && (
                  <div className="mt-4 text-sm text-muted-foreground">
                    {filteredFAQs.length} soru bulundu
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* FAQ Items */}
          <div className={`space-y-4 mb-16 transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            {filteredFAQs.length === 0 ? (
              <Card className="bg-card/60 backdrop-blur-sm border-border/20">
                <CardContent className="p-12 text-center">
                  <Search className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" />
                  <h3 className="text-lg font-semibold text-foreground mb-2">Aradığınız soru bulunamadı</h3>
                  <p className="text-muted-foreground mb-4">Farklı anahtar kelimeler deneyebilir veya kategori seçimini değiştirebilirsiniz.</p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm("");
                      setSelectedCategory("all");
                    }}
                  >
                    Filtreleri Temizle
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredFAQs.map((faq, index) => {
                const category = categories.find(cat => cat.id === faq.category);
                const isOpen = openItems.has(index);
                
                return (
                  <Card key={index} className="bg-white/60 backdrop-blur-sm border-white/20 hover:bg-white/70 transition-all overflow-hidden">
                    <button
                      onClick={() => toggleItem(index)}
                      className="w-full p-6 text-left flex items-center justify-between hover:bg-white/20 transition-colors"
                    >
                      <div className="flex items-center gap-4 flex-1">
                        {faq.popular && (
                          <Badge className="bg-gradient-to-r from-primary to-blue-600 text-white text-xs px-2 py-1">
                            Popüler
                          </Badge>
                        )}
                        <h3 className="text-lg font-semibold text-foreground pr-4 flex-1">
                          {faq.question}
                        </h3>
                        {category && (
                          <Badge variant="outline" className="bg-white/60 border-white/30 text-xs">
                            {category.label}
                          </Badge>
                        )}
                      </div>
                      <ChevronDown className={`h-5 w-5 text-primary flex-shrink-0 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
                    </button>
                    
                    {isOpen && (
                      <div className="px-6 pb-6 text-muted-foreground leading-relaxed">
                        {faq.answer}
                      </div>
                    )}
                  </Card>
                );
              })
            )}
          </div>

          {/* Contact CTA */}
          <div className={`text-center transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Card className="bg-gradient-to-r from-primary to-blue-600 text-white border-0">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold mb-4">
                  Başka sorularınız mı var?
                </h3>
                <p className="text-white/90 mb-6 max-w-2xl mx-auto">
                  Uzman ekibimiz size yardımcı olmaktan mutluluk duyar. 7/24 Türkçe destek hizmetimizden faydalanın.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-white text-primary hover:bg-white/90">
                    <a href="mailto:<EMAIL>">
                      <Headphones className="mr-2 h-4 w-4" />
                      Email Gönder
                    </a>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-white/30 bg-white/10 text-white hover:bg-white/20">
                    <a href="tel:+905001234567">
                      Telefon: 0500 123 45 67
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
