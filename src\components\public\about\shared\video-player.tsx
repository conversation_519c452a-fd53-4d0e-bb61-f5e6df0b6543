"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Play, Pause, Volume2, VolumeX } from "lucide-react";
import { cn } from "@/lib/utils";

interface VideoPlayerProps {
  src?: string;
  poster?: string;
  title: string;
  description?: string;
  duration?: string;
  autoPlay?: boolean;
  muted?: boolean;
  className?: string;
}

export function VideoPlayer({
  src,
  poster,
  title,
  description,
  duration,
  autoPlay = false,
  muted = true,
  className,
}: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [showControls, setShowControls] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  // Placeholder video since we don't have actual video files
  const hasVideo = src && src.length > 0;

  if (!hasVideo) {
    return (
      <Card className={cn("relative aspect-video bg-muted overflow-hidden", className)}>
        <div className="absolute inset-0 flex flex-col items-center justify-center space-y-4 p-6">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
            <Play className="h-8 w-8 text-primary" />
          </div>
          <div className="text-center space-y-2">
            <h3 className="font-semibold text-lg">{title}</h3>
            {description && (
              <p className="text-muted-foreground text-sm">{description}</p>
            )}
            {duration && (
              <p className="text-xs text-muted-foreground">{duration}</p>
            )}
          </div>
          <Button variant="outline" size="sm" disabled>
            Video Yakında Gelecek
          </Button>
        </div>
        
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent" />
      </Card>
    );
  }

  return (
    <Card className={cn("relative aspect-video overflow-hidden group", className)}>
      <video
        ref={videoRef}
        className="w-full h-full object-cover"
        poster={poster}
        autoPlay={autoPlay}
        muted={muted}
        loop
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        onMouseEnter={() => setShowControls(true)}
        onMouseLeave={() => setShowControls(false)}
      >
        <source src={src} type="video/mp4" />
        Tarayıcınız video oynatmayı desteklemiyor.
      </video>

      {/* Video Controls Overlay */}
      <div className={cn(
        "absolute inset-0 bg-black/20 flex items-center justify-center transition-opacity duration-300",
        showControls ? "opacity-100" : "opacity-0 group-hover:opacity-100"
      )}>
        <div className="flex items-center space-x-4">
          <Button
            size="lg"
            variant="secondary"
            className="bg-white/90 hover:bg-white text-black"
            onClick={togglePlay}
          >
            {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
          </Button>
          
          <Button
            size="sm"
            variant="secondary"
            className="bg-white/90 hover:bg-white text-black"
            onClick={toggleMute}
          >
            {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Video Info Overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
        <div className="text-white">
          <h3 className="font-semibold mb-1">{title}</h3>
          {description && (
            <p className="text-sm text-white/80">{description}</p>
          )}
        </div>
      </div>

      {/* Duration Badge */}
      {duration && (
        <div className="absolute top-4 right-4 bg-black/60 text-white text-xs px-2 py-1 rounded">
          {duration}
        </div>
      )}
    </Card>
  );
}