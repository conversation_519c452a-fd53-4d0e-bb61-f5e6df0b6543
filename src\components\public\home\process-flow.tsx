'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AnimatedSection } from '@/components/ui/animated-section';
import {
  UserPlus,
  Settings,
  Rocket,
  ArrowRight,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';

export function ProcessFlow() {
  const steps = [
    {
      icon: UserPlus,
      title: '<PERSON><PERSON><PERSON>l',
      description: 'Ücretsiz hesap oluştur ve İlk Ay deneme süresini başlat',
      duration: '2 dakika',
      color: 'from-blue-500/20 to-blue-600/10',
      borderColor: 'border-blue-200/50'
    },
    {
      icon: Settings,
      title: '<PERSON><PERSON><PERSON> Yap',
      description: 'Salon bilgilerini ekle, paketleri tanımla ve ayarları yapılandır',
      duration: '15 dakika',
      color: 'from-purple-500/20 to-purple-600/10',
      borderColor: 'border-purple-200/50'
    },
    {
      icon: Rocket,
      title: '<PERSON><PERSON><PERSON>ya Başla',
      description: '<PERSON><PERSON>lerini davet et ve dijital dönüşümün keyfini çı<PERSON>',
      duration: 'Anında',
      color: 'from-green-500/20 to-green-600/10',
      borderColor: 'border-green-200/50'
    }
  ];

  const benefits = [
    {
      icon: CheckCircle,
      text: 'Teknik bilgi gerektirmez'
    },
    {
      icon: Clock,
      text: '24 saat içinde aktif'
    },
    {
      icon: Zap,
      text: 'Anında kullanıma hazır'
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-7xl">
          {/* Section Header */}
          <div className="mb-16 text-center">
            <AnimatedSection animation="fade-up" delay={100}>
              <Badge variant="outline" className="mb-4 px-4 py-2 border-primary/20 bg-primary/10">
                <Rocket className="mr-2 h-4 w-4" />
                Nasıl Çalışır?
              </Badge>
              
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                  3 Adımda
                </span>
                <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  Başla ve Kullan
                </span>
              </h2>
              
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Karmaşık kurulum süreçleri yok. Sadece 3 basit adımda salonunu dijitalleştir ve yönetim süreçlerini otomatikleştir.
              </p>
            </AnimatedSection>
          </div>

          {/* Process Steps */}
          <div className="relative mb-16">
            {/* Connection lines */}
            <div className="absolute top-8 left-1/2 hidden lg:block w-full max-w-4xl -translate-x-1/2">
              <div className="flex justify-between">
                <div className="w-1/3 h-0.5 bg-gradient-to-r from-blue-500/30 to-purple-500/30 mt-8" />
                <div className="w-1/3 h-0.5 bg-gradient-to-r from-purple-500/30 to-green-500/30 mt-8" />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              {steps.map((step, index) => {
                const IconComponent = step.icon;
                return (
                  <AnimatedSection
                    key={index}
                    animation="fade-up"
                    delay={200 + (index * 200)}
                  >
                    <div className="relative text-center">
                      {/* Step number */}
                      <div className="absolute -top-4 -left-4 z-10 flex h-8 w-8 items-center justify-center rounded-full bg-primary text-sm font-bold text-primary-foreground">
                        {index + 1}
                      </div>

                      <div className={`group relative overflow-hidden rounded-2xl border p-8 ${step.borderColor} bg-gradient-to-br ${step.color} backdrop-blur transition-all duration-500 hover:border-primary/40 hover:-translate-y-2 hover:shadow-xl`}>
                        {/* Background glow */}
                        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100" />

                        <div className="relative">
                          <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-2xl bg-background/80 backdrop-blur transition-transform duration-300 group-hover:scale-110">
                            <IconComponent className="h-10 w-10 text-primary" />
                          </div>

                          <h3 className="mb-4 text-2xl font-bold text-foreground">
                            {step.title}
                          </h3>

                          <p className="mb-6 text-muted-foreground leading-relaxed">
                            {step.description}
                          </p>

                          <Badge
                            variant="secondary"
                            className="bg-background/80 text-foreground border-primary/20"
                          >
                            <Clock className="mr-1 h-3 w-3" />
                            {step.duration}
                          </Badge>
                        </div>

                        {/* Arrow for connection (mobile) */}
                        {index < steps.length - 1 && (
                          <div className="absolute -bottom-4 left-1/2 lg:hidden">
                            <ArrowRight className="h-6 w-6 text-primary -translate-x-1/2 rotate-90" />
                          </div>
                        )}
                      </div>
                    </div>
                  </AnimatedSection>
                );
              })}
            </div>
          </div>

          {/* Benefits & CTA */}
          <AnimatedSection animation="fade-up" delay={800}>
            <div className="text-center">
              <div className="mb-8 flex flex-wrap items-center justify-center gap-6">
                {benefits.map((benefit, index) => {
                  const IconComponent = benefit.icon;
                  return (
                    <div
                      key={index}
                      className="flex items-center gap-2 rounded-full border border-primary/20 bg-background/80 px-4 py-2 backdrop-blur"
                    >
                      <IconComponent className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">{benefit.text}</span>
                    </div>
                  );
                })}
              </div>

              <div className="flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
                <Button
                  asChild
                  size="lg"
                  className="group h-12 px-8 text-base font-semibold shadow-lg transition-all duration-300 hover:shadow-xl"
                >
                  <Link href="/onboarding">
                    Hemen Başla
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>

                <Button
                  asChild
                  variant="ghost"
                  size="lg"
                  className="group h-12 px-8 text-base font-semibold text-primary hover:bg-primary/10"
                >
                  <Link href="/features">
                    Tüm özellikleri keşfet
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              </div>

              <p className="mt-6 text-sm text-muted-foreground">
                Kredi kartı bilgisi gerekmez • İlk Ay ücretsiz deneme • İstediğin zaman iptal et
              </p>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
}