'use client';

import {
  <PERSON><PERSON><PERSON>,
  Users,
  Package,
  BarChart3,
  Calendar,
  MapPin,
  Shield,
  Smartphone,
  Bell,
  Settings,
  ArrowRight,
  CheckCircle,
  Star,
  Zap,
  Clock,
  TrendingUp,
  UserCheck,
  FileText,
  Eye,
  PlayCircle,

  Search,
  Grid3X3,
  List,
  X,
  ExternalLink,
  Download,
  Heart,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useState, useMemo } from 'react';
import Link from 'next/link';



export function FeaturesGrid() {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const [favorites, setFavorites] = useState<string[]>([]);
  const [comparisonFeatures, setComparisonFeatures] = useState<string[]>([]);
  const [showComparison, setShowComparison] = useState(false);
  const featureCategories = useMemo(() => [
    { id: 'all', name: 'Tümü', icon: Grid3X3, count: 0 },
    { id: 'core', name: 'Temel Yönetim', icon: Settings, count: 0 },
    { id: 'analytics', name: 'Analitik & Raporlar', icon: BarChart3, count: 0 },
    { id: 'user', name: 'Kullanıcı Deneyimi', icon: Users, count: 0 },
    { id: 'business', name: 'İş Geliştirme', icon: TrendingUp, count: 0 },
  ], []);

  const mainFeatures = useMemo(() => [
    {
      id: 'gym-management',
      icon: Dumbbell,
      title: 'Salon Yönetimi',
      category: 'core',
      description: 'Salonunuzu dijital ortamda yönetin, üye ve paket takibini kolayca yapın.',
      details: [
        'Salon profili ve bilgi yönetimi',
        'Paket ve hizmet tanımlamaları',
        'Kapasite ve kaynak planlaması',
        'Çoklu salon desteği',
      ],
      benefits: [
        '%70 zaman tasarrufu yönetim işlerinde',
        'Merkezi yönetim panosu',
        'Otomatik raporlama'
      ],
      color: 'from-blue-500/10 to-blue-600/10',
      borderColor: 'border-blue-200/50',
      demoUrl: '/demo/gym-management',
      screenshots: ['/screenshots/gym-dashboard.jpg'],
      useCases: ['Tek salon işletmeciliği', 'Çoklu şube yönetimi'],
      implementation: '1-2 gün',
      roi: '%40 verimlilik artışı'
    },
    {
      id: 'member-management',
      icon: Users,
      title: 'Üye Yönetimi',
      category: 'core',
      description: 'Üyelerinizi takip edin, katılım isteklerini onaylayın ve iletişimde kalın.',
      details: [
        'Kapsamlı üye profilleri',
        'Üyelik durumu takibi',
        'Katılım onay sistemi',
        'İletişim ve bildirimler',
      ],
      benefits: [
        'Anında üye durumu görüntüleme',
        'Otomatik üyelik yenilemeleri',
        'Kapsamlı üye geçmişi'
      ],
      color: 'from-green-500/10 to-green-600/10',
      borderColor: 'border-green-200/50',
      demoUrl: '/demo/member-management',
      screenshots: ['/screenshots/member-dashboard.jpg'],
      useCases: ['Üye kayıt işlemleri', 'Üye takip sistemi'],
      implementation: '1 gün',
      roi: '%50 üye memnuniyeti artışı'
    },
    {
      id: 'package-management',
      icon: Package,
      title: 'Paket Yönetimi',
      category: 'core',
      description: 'Üyelik ve oturum bazlı paketlerin kurgulanması ve yönetimi.',
      details: [
        'Paket tanımları',
        'Kayıt ve atama akışları',
        'Paket geçmişi takibi',
        'İndirim ve kampanyalar',
      ],
      benefits: [
        'Esnek paket yapılandırması',
        'Otomatik faturalama',
        'Kampanya yönetimi'
      ],
      color: 'from-purple-500/10 to-purple-600/10',
      borderColor: 'border-purple-200/50',
      demoUrl: '/demo/package-management',
      screenshots: ['/screenshots/package-config.jpg'],
      useCases: ['Paket satışı', 'Fiyat yönetimi'],
      implementation: '2-3 gün',
      roi: '%30 gelir artışı'
    },
    {
      id: 'analytics-reports',
      icon: BarChart3,
      title: 'Analitik Raporlar',
      category: 'analytics',
      description:
        'Detaylı raporlar ve analizlerle işletmenizin performansını ölçün.',
      details: [
        'Gelir ve gider analizleri',
        'Üye aktivite raporları',
        'Performans metrikleri',
        'Trend analizleri',
      ],
      benefits: [
        'Gerçek zamanlı iş zekası',
        'Öngörülebilir analizler',
        'Otomatik raporlama'
      ],
      color: 'from-orange-500/10 to-orange-600/10',
      borderColor: 'border-orange-200/50',
      demoUrl: '/demo/analytics',
      screenshots: ['/screenshots/analytics-dashboard.jpg'],
      useCases: ['Performans analizi', 'Karar destek sistemi'],
      implementation: '1 gün',
      roi: '%60 daha hızlı karar alma'
    },
    {
      id: 'appointment-system',
      icon: Calendar,
      title: 'Randevu Sistemi',
      category: 'user',
      description:
        'Online randevu sistemi ile üyelerinizin kolayca rezervasyon yapmasını sağlayın.',
      details: [
        'Online randevu alma',
        'Antrenör programı yönetimi',
        'Otomatik hatırlatmalar',
        'Grup dersleri planlaması',
      ],
      benefits: [
        '%90 daha az iptal oranları',
        'Otomatik hatırlatma sistemi',
        'Kapasite optimizasyonu'
      ],
      color: 'from-red-500/10 to-red-600/10',
      borderColor: 'border-red-200/50',
      demoUrl: '/demo/appointments',
      screenshots: ['/screenshots/appointment-calendar.jpg'],
      useCases: ['Antrenör randevuları', 'Grup ders planları'],
      implementation: '2 gün',
      roi: '%45 kapasite kullanım artışı'
    },
    {
      id: 'gym-discovery',
      icon: MapPin,
      title: 'Salon Keşfi',
      category: 'business',
      description:
        'Size en yakın ve en uygun spor salonlarını harita üzerinde keşfedin.',
      details: [
        'Harita tabanlı arama',
        'Mesafe ve konum filtreleri',
        'Salon detay sayfaları',
        'Değerlendirme sistemi',
      ],
      benefits: [
        'Artan çevrimiçi görünürlük',
        'Yeni üye kazanımı',
        'Rekabet avantajı'
      ],
      color: 'from-teal-500/10 to-teal-600/10',
      borderColor: 'border-teal-200/50',
      demoUrl: '/demo/gym-discovery',
      screenshots: ['/screenshots/gym-map.jpg'],
      useCases: ['Salon bulma', 'Üye kazanımı'],
      implementation: '1 gün',
      roi: '%25 yeni üye artışı'
    }
  ], []);

  // Filter and search logic
  const filteredFeatures = useMemo(() => {
    let filtered = mainFeatures;

    // Filter by category
    if (activeCategory !== 'all') {
      filtered = filtered.filter(feature => feature.category === activeCategory);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(feature =>
        feature.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        feature.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        feature.details.some(detail => detail.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    return filtered;
  }, [activeCategory, searchTerm, mainFeatures]);

  // Update category counts
  const categoriesWithCounts = useMemo(() => {
    const counts = mainFeatures.reduce((acc, feature) => {
      acc[feature.category] = (acc[feature.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return featureCategories.map(category => ({
      ...category,
      count: category.id === 'all' ? mainFeatures.length : (counts[category.id] || 0)
    }));
  }, [featureCategories, mainFeatures]);

  const toggleFavorite = (featureId: string) => {
    setFavorites(prev => 
      prev.includes(featureId) 
        ? prev.filter(id => id !== featureId)
        : [...prev, featureId]
    );
  };

  const toggleComparison = (featureId: string) => {
    setComparisonFeatures(prev => {
      if (prev.includes(featureId)) {
        return prev.filter(id => id !== featureId);
      } else if (prev.length < 3) {
        return [...prev, featureId];
      }
      return prev; // Max 3 features for comparison
    });
  };

  const clearComparison = () => {
    setComparisonFeatures([]);
    setShowComparison(false);
  };

  const additionalFeatures = useMemo(() => [
    {
      icon: Shield,
      title: 'Güvenli Altyapı',
      description: 'SSL şifreleme ve güvenli veri saklama',
    },
    {
      icon: Smartphone,
      title: 'Mobil Uyumlu',
      description: 'Her cihazdan erişilebilir responsive tasarım',
    },
    {
      icon: Bell,
      title: 'Bildirim Sistemi',
      description: 'Otomatik SMS ve e-posta bildirimleri',
    },
    {
      icon: Settings,
      title: 'Özelleştirme',
      description: 'İhtiyaçlarınıza göre özelleştirilebilir',
    },
    {
      icon: Clock,
      title: '7/24 Erişim',
      description: 'Kesintisiz hizmet ve sürekli erişim',
    },
    {
      icon: TrendingUp,
      title: 'Büyüme Desteği',
      description: 'Ölçeklenebilir altyapı ve çözümler',
    },
    {
      icon: UserCheck,
      title: 'Kolay Kullanım',
      description: 'Sezgisel arayüz ve kullanıcı dostu tasarım',
    },
    {
      icon: FileText,
      title: 'Raporlama',
      description: 'Detaylı raporlar ve veri analizi',
    },
  ], []);

  return (
    <section className="bg-background py-20 lg:py-32">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-7xl">
          {/* Enhanced Section Header */}
          <div className="mb-16 text-center">
            <Badge
              variant="outline"
              className="mb-6 px-6 py-3 text-sm font-medium bg-background/50 backdrop-blur-sm border-primary/20"
            >
              <Star className="mr-2 h-4 w-4 animate-pulse" />
              İnteraktif Özellik Keşfi
            </Badge>
            <h2 className="text-primary mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
              İşletmenizi Güçlendirecek
              <span className="block bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Akıllı Özellikler
              </span>
            </h2>
            <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed mb-8">
              Spor salonu yönetiminin her alanında size yardımcı olacak kapsamlı özellik seti.
              <span className="text-primary font-semibold"> Kategorilere göre keşfedin</span> ve ihtiyaçlarınıza en uygun çözümleri bulun.
            </p>
            
            {/* Search and Filter Bar */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Özellik ara... (örn: üye, randevu, rapor)"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 bg-background/50 backdrop-blur-sm border-border/50 focus:border-primary/30"
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="px-3"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="px-3"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Category Filter Buttons */}
          <div className="mb-12">
            <div className="flex flex-wrap justify-center gap-2 mb-8 bg-muted/30 p-2 rounded-xl">
              {categoriesWithCounts.map((category) => {
                const IconComponent = category.icon;
                const isActive = activeCategory === category.id;
                return (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`flex items-center gap-2 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-background/50 ${
                      isActive ? 'bg-primary text-primary-foreground' : 'bg-background/50'
                    }`}
                  >
                    <IconComponent className="h-4 w-4" />
                    <span className="hidden sm:inline">{category.name}</span>
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {category.count}
                    </Badge>
                  </button>
                );
              })}
            </div>

            <div className="mt-8">
              {filteredFeatures.length === 0 ? (
                <div className="text-center py-12">
                  <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Hiç sonuç bulunamadı</h3>
                  <p className="text-muted-foreground mb-4">
                    Arama kriterlerinizi değiştirmeyi deneyin
                  </p>
                  <Button onClick={() => { setSearchTerm(''); setActiveCategory('all'); }}>
                    Tüm Özellikleri Görüntüle
                  </Button>
                </div>
              ) : (
                <div className={`grid gap-8 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 max-w-4xl mx-auto'}`}>
                  {filteredFeatures.map((feature) => {
                    const IconComponent = feature.icon;
                    const isFavorite = favorites.includes(feature.id);
                    
                    return (
                      <article
                        key={feature.id}
                        className={`group relative bg-gradient-to-br ${feature.color} rounded-2xl border ${feature.borderColor} hover:border-primary/30 transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl ${viewMode === 'list' ? 'flex items-center p-6' : 'p-8'}`}
                      >
                        <div className="from-primary/5 absolute inset-0 rounded-2xl bg-gradient-to-br to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100"></div>

                        <div className="relative z-10 flex-1">
                          {/* Feature Icon and Actions */}
                          <div className={`flex ${viewMode === 'list' ? 'items-center mb-0 mr-6' : 'items-center justify-between mb-6'}`}>
                            <div className="bg-primary/10 group-hover:bg-primary/20 flex h-16 w-16 items-center justify-center rounded-2xl transition-colors duration-300">
                              <IconComponent className="text-primary h-8 w-8" />
                            </div>
                            
                            <div className="flex items-center gap-1">
                              {/* Comparison Checkbox */}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleComparison(feature.id)}
                                className={`transition-colors ${
                                  comparisonFeatures.includes(feature.id) 
                                    ? 'text-blue-500 hover:text-blue-600' 
                                    : 'text-muted-foreground hover:text-blue-500'
                                }`}
                                disabled={!comparisonFeatures.includes(feature.id) && comparisonFeatures.length >= 3}
                              >
                                <div className={`w-4 h-4 border-2 rounded ${comparisonFeatures.includes(feature.id) ? 'bg-blue-500 border-blue-500' : 'border-muted-foreground'} flex items-center justify-center`}>
                                  {comparisonFeatures.includes(feature.id) && (
                                    <CheckCircle className="h-3 w-3 text-white" />
                                  )}
                                </div>
                              </Button>
                              
                              {/* Favorite Button */}
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleFavorite(feature.id)}
                                className={`transition-colors ${isFavorite ? 'text-red-500 hover:text-red-600' : 'text-muted-foreground hover:text-red-500'}`}
                              >
                                <Heart className={`h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
                              </Button>
                            </div>
                          </div>

                          {/* Feature Content */}
                          <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>
                            <div className={`${viewMode === 'list' ? 'flex items-start justify-between' : ''}`}>
                              <div className="flex-1">
                                <h3 className="text-primary mb-3 text-xl font-bold group-hover:text-primary/90 transition-colors">
                                  {feature.title}
                                </h3>

                                <p className="text-muted-foreground mb-4 leading-relaxed">
                                  {feature.description}
                                </p>

                                {/* ROI and Implementation Info */}
                                <div className="flex flex-wrap gap-2 mb-4">
                                  <Badge variant="secondary" className="text-xs">
                                    <TrendingUp className="mr-1 h-3 w-3" />
                                    {feature.roi}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    <Clock className="mr-1 h-3 w-3" />
                                    {feature.implementation}
                                  </Badge>
                                </div>
                              </div>

                              {/* Action Buttons */}
                              {viewMode === 'list' && (
                                <div className="flex flex-col gap-2 ml-4">
                                  <Dialog>
                                    <DialogTrigger asChild>
                                      <Button size="sm" className="whitespace-nowrap">
                                        <Eye className="mr-2 h-4 w-4" />
                                        Önizleme
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                                      <DialogHeader>
                                        <DialogTitle className="flex items-center gap-2">
                                          <IconComponent className="h-6 w-6 text-primary" />
                                          {feature.title}
                                        </DialogTitle>
                                      </DialogHeader>
                                      
                                      <div className="space-y-6">
                                        {/* Feature Details */}
                                        <div>
                                          <h4 className="font-semibold mb-3">Özellik Detayları</h4>
                                          <ul className="space-y-2">
                                            {feature.details.map((detail, detailIndex) => (
                                              <li key={detailIndex} className="flex items-center text-sm">
                                                <CheckCircle className="text-primary mr-2 h-4 w-4 flex-shrink-0" />
                                                {detail}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                        
                                        {/* Benefits */}
                                        <div>
                                          <h4 className="font-semibold mb-3">Faydalar</h4>
                                          <ul className="space-y-2">
                                            {feature.benefits.map((benefit, benefitIndex) => (
                                              <li key={benefitIndex} className="flex items-center text-sm text-green-700 dark:text-green-400">
                                                <Star className="mr-2 h-4 w-4 flex-shrink-0" />
                                                {benefit}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                        
                                        {/* Use Cases */}
                                        <div>
                                          <h4 className="font-semibold mb-3">Kullanım Alanları</h4>
                                          <div className="flex flex-wrap gap-2">
                                            {feature.useCases.map((useCase, index) => (
                                              <Badge key={index} variant="secondary">{useCase}</Badge>
                                            ))}
                                          </div>
                                        </div>
                                        
                                        {/* Demo CTA */}
                                        <div className="flex gap-2 pt-4">
                                          <Button asChild className="flex-1">
                                            <a href={feature.demoUrl} target="_blank" rel="noopener noreferrer">
                                              <PlayCircle className="mr-2 h-4 w-4" />
                                              Canlı Demo Görün
                                            </a>
                                          </Button>
                                          <Button variant="outline" asChild>
                                            <a href="/auth/register">
                                              Başlayın
                                              <ArrowRight className="ml-2 h-4 w-4" />
                                            </a>
                                          </Button>
                                        </div>
                                      </div>
                                    </DialogContent>
                                  </Dialog>
                                  
                                  <Button variant="outline" size="sm">
                                    <Download className="mr-2 h-4 w-4" />
                                    Bilgi Al
                                  </Button>
                                </div>
                              )}
                            </div>

                            {/* Grid View Details */}
                            {viewMode === 'grid' && (
                              <>
                                <ul className="mb-6 space-y-2">
                                  {feature.details.slice(0, 3).map((detail, detailIndex) => (
                                    <li
                                      key={detailIndex}
                                      className="text-muted-foreground flex items-center text-sm"
                                    >
                                      <CheckCircle className="text-primary mr-2 h-4 w-4 flex-shrink-0" />
                                      {detail}
                                    </li>
                                  ))}
                                  {feature.details.length > 3 && (
                                    <li className="text-muted-foreground text-sm ml-6">
                                      +{feature.details.length - 3} daha fazla...
                                    </li>
                                  )}
                                </ul>

                                <div className="flex items-center justify-between">
                                  <Dialog>
                                    <DialogTrigger asChild>
                                      <Button variant="ghost" size="sm" className="text-primary hover:text-primary/80 p-0">
                                        <Eye className="mr-2 h-4 w-4" />
                                        Detayları Gör
                                        <ArrowRight className="ml-2 h-4 w-4" />
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                                      <DialogHeader>
                                        <DialogTitle className="flex items-center gap-2">
                                          <IconComponent className="h-6 w-6 text-primary" />
                                          {feature.title}
                                        </DialogTitle>
                                      </DialogHeader>
                                      
                                      <div className="space-y-6">
                                        {/* Feature Details */}
                                        <div>
                                          <h4 className="font-semibold mb-3">Özellik Detayları</h4>
                                          <ul className="space-y-2">
                                            {feature.details.map((detail, detailIndex) => (
                                              <li key={detailIndex} className="flex items-center text-sm">
                                                <CheckCircle className="text-primary mr-2 h-4 w-4 flex-shrink-0" />
                                                {detail}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                        
                                        {/* Benefits */}
                                        <div>
                                          <h4 className="font-semibold mb-3">Faydalar</h4>
                                          <ul className="space-y-2">
                                            {feature.benefits.map((benefit, benefitIndex) => (
                                              <li key={benefitIndex} className="flex items-center text-sm text-green-700 dark:text-green-400">
                                                <Star className="mr-2 h-4 w-4 flex-shrink-0" />
                                                {benefit}
                                              </li>
                                            ))}
                                          </ul>
                                        </div>
                                        
                                        {/* Use Cases */}
                                        <div>
                                          <h4 className="font-semibold mb-3">Kullanım Alanları</h4>
                                          <div className="flex flex-wrap gap-2">
                                            {feature.useCases.map((useCase, index) => (
                                              <Badge key={index} variant="secondary">{useCase}</Badge>
                                            ))}
                                          </div>
                                        </div>
                                        
                                        {/* Demo CTA */}
                                        <div className="flex gap-2 pt-4">
                                          <Button asChild className="flex-1">
                                            <a href={feature.demoUrl} target="_blank" rel="noopener noreferrer">
                                              <PlayCircle className="mr-2 h-4 w-4" />
                                              Canlı Demo Görün
                                            </a>
                                          </Button>
                                          <Button variant="outline" asChild>
                                            <a href="/auth/register">
                                              Başlayın
                                              <ArrowRight className="ml-2 h-4 w-4" />
                                            </a>
                                          </Button>
                                        </div>
                                      </div>
                                    </DialogContent>
                                  </Dialog>
                                  
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    asChild
                                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                  >
                                    <a href={feature.demoUrl} target="_blank" rel="noopener noreferrer">
                                      <ExternalLink className="h-4 w-4" />
                                    </a>
                                  </Button>
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      </article>
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Floating Comparison Bar */}
          {comparisonFeatures.length > 0 && (
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
              <div className="bg-background/95 backdrop-blur-sm border border-border/50 rounded-2xl shadow-2xl p-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <div className="font-semibold text-sm">
                        Karşılaştırma ({comparisonFeatures.length}/3)
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Seçili özellikler karşılaştırılıyor
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button 
                      size="sm" 
                      onClick={() => setShowComparison(true)}
                      disabled={comparisonFeatures.length < 2}
                    >
                      Karşılaştır
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={clearComparison}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Comparison Modal */}
          <Dialog open={showComparison} onOpenChange={setShowComparison}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <BarChart3 className="h-6 w-6 text-primary" />
                  Özellik Karşılaştırması
                </DialogTitle>
              </DialogHeader>
              
              {comparisonFeatures.length >= 2 && (
                <div className="space-y-6">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-48">Özellik</TableHead>
                        {comparisonFeatures.map((featureId) => {
                          const feature = mainFeatures.find(f => f.id === featureId);
                          if (!feature) return null;
                          const IconComponent = feature.icon;
                          return (
                            <TableHead key={featureId} className="text-center min-w-48">
                              <div className="flex flex-col items-center gap-2">
                                <IconComponent className="h-8 w-8 text-primary" />
                                <span className="font-bold">{feature.title}</span>
                              </div>
                            </TableHead>
                          );
                        })}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {/* Description Row */}
                      <TableRow>
                        <TableCell className="font-semibold">Açıklama</TableCell>
                        {comparisonFeatures.map((featureId) => {
                          const feature = mainFeatures.find(f => f.id === featureId);
                          if (!feature) return null;
                          return (
                            <TableCell key={featureId} className="text-sm">
                              {feature.description}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                      
                      {/* Category Row */}
                      <TableRow>
                        <TableCell className="font-semibold">Kategori</TableCell>
                        {comparisonFeatures.map((featureId) => {
                          const feature = mainFeatures.find(f => f.id === featureId);
                          if (!feature) return null;
                          const category = featureCategories.find(cat => cat.id === feature.category);
                          return (
                            <TableCell key={featureId}>
                              <Badge variant="secondary">{category?.name}</Badge>
                            </TableCell>
                          );
                        })}
                      </TableRow>
                      
                      {/* Implementation Time Row */}
                      <TableRow>
                        <TableCell className="font-semibold">Kurulum Süresi</TableCell>
                        {comparisonFeatures.map((featureId) => {
                          const feature = mainFeatures.find(f => f.id === featureId);
                          if (!feature) return null;
                          return (
                            <TableCell key={featureId}>
                              <Badge variant="outline">
                                <Clock className="mr-1 h-3 w-3" />
                                {feature.implementation}
                              </Badge>
                            </TableCell>
                          );
                        })}
                      </TableRow>
                      
                      {/* ROI Row */}
                      <TableRow>
                        <TableCell className="font-semibold">ROI Etkisi</TableCell>
                        {comparisonFeatures.map((featureId) => {
                          const feature = mainFeatures.find(f => f.id === featureId);
                          if (!feature) return null;
                          return (
                            <TableCell key={featureId}>
                              <Badge variant="outline" className="text-green-600">
                                <TrendingUp className="mr-1 h-3 w-3" />
                                {feature.roi}
                              </Badge>
                            </TableCell>
                          );
                        })}
                      </TableRow>
                      
                      {/* Benefits Comparison */}
                      <TableRow>
                        <TableCell className="font-semibold align-top pt-4">Ana Faydalar</TableCell>
                        {comparisonFeatures.map((featureId) => {
                          const feature = mainFeatures.find(f => f.id === featureId);
                          if (!feature) return null;
                          return (
                            <TableCell key={featureId} className="align-top pt-4">
                              <ul className="space-y-1">
                                {feature.benefits.map((benefit, index) => (
                                  <li key={index} className="flex items-start gap-1 text-xs">
                                    <Star className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                                    <span>{benefit}</span>
                                  </li>
                                ))}
                              </ul>
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    </TableBody>
                  </Table>
                  
                  <div className="flex gap-2 justify-center pt-6 border-t">
                    <Button asChild className="flex-1">
                      <Link href="/auth/register">
                        Tüm Özelliklerle Başlayın
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                    <Button variant="outline" onClick={clearComparison}>
                      Karşılaştırmayı Temizle
                    </Button>
                  </div>
                </div>
              )}
            </DialogContent>
          </Dialog>

          {/* Additional Features */}
          <div className="bg-muted/30 border-border rounded-2xl border p-8 md:p-12">
            <div className="mb-12 text-center">
              <Badge
                variant="outline"
                className="mb-4 px-4 py-2 text-sm font-medium"
              >
                <Zap className="mr-2 h-4 w-4" />
                Ek Avantajlar
              </Badge>
              <h3 className="text-primary mb-4 text-2xl font-bold md:text-3xl">
                Sportiva&apos;yı Özel Kılan Özellikler
              </h3>
              <p className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed">
                Güvenlik, performans ve kullanıcı deneyimi odaklı ek
                özelliklerle farkı hissedin
              </p>
            </div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
              {additionalFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="group text-center">
                    <div className="bg-primary/10 group-hover:bg-primary/20 mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl transition-colors duration-300">
                      <IconComponent className="text-primary h-8 w-8" />
                    </div>
                    <h4 className="text-foreground mb-3 font-semibold">
                      {feature.title}
                    </h4>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
