"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, Quote, TrendingUp, Users, Award, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

// Static data moved outside component to prevent recreation
const testimonials = [
  {
    id: 1,
    name: "<PERSON>met Kilic",
    role: "Salon Sahibi",
    gym: "FitLife Spor Merkezi",
    plan: "Gelisim",
    rating: 5,
    content: "Sportiva ya gectikten sonra uye memnuniyetimiz %40 artti. Ozellikle randevu sistemi ve uye takibi konusunda cok verimli. 6 ayda maliyetini cikardi.",
    metrics: {
      memberIncrease: "+35%",
      revenueGrowth: "+25,000 TL",
      timeSaved: "15 saat/hafta"
    }
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON><PERSON>mir",
    role: "<PERSON><PERSON>du<PERSON>",
    gym: "PowerGym Zinciri",
    plan: "Kurumsal",
    rating: 5,
    content: "5 subemizde kullaniyoruz. Merkezi yonetim imkani sayesinde operasyonel verimliligimiz ikiye katlandi. ROI hesabimiz %300 un uzerinde.",
    metrics: {
      memberIncrease: "+60%",
      revenueGrowth: "+120,000 TL",
      timeSaved: "40 saat/hafta"
    }
  },
  {
    id: 3,
    name: "Murat Ozkan",
    role: "Kurucu Ortak",
    gym: "Elite Fitness",
    plan: "Gelisim",
    rating: 5,
    content: "Dijitallesme surecimizde en dogru tercihi yaptik. Uye kaybimizi %50 azalttik, yeni uye kazanimiz ise %25 artti. Kesinlikle tavsiye ederim.",
    metrics: {
      memberIncrease: "+25%",
      revenueGrowth: "+18,500 TL",
      timeSaved: "12 saat/hafta"
    }
  },
  {
    id: 4,
    name: "Ayse Yilmaz",
    role: "Isletme Muduru",
    gym: "BodyTech Spor Salonu",
    plan: "Gelisim",
    rating: 5,
    content: "Ilk ay biraz adapte olmakta zorlandik ama destek ekibi cok yardimci oldu. Simdi islerimiz cok daha duzenli, raporlama sistemi harika.",
    metrics: {
      memberIncrease: "+20%",
      revenueGrowth: "+15,000 TL",
      timeSaved: "10 saat/hafta"
    }
  }
];

export function PricingTestimonials() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, []); // testimonials is static, no need to add as dependency

  const stats = [
    {
      icon: Users,
      value: "500+",
      label: "Aktif Salon",
      description: "Turkiye genelinde"
    },
    {
      icon: TrendingUp,
      value: "%89",
      label: "Musteri Memnuniyeti",
      description: "4.8/5 puan ortalamasi"
    },
    {
      icon: Award,
      value: "2.5M+ TL",
      label: "Toplam Tasarruf",
      description: "Musterilerimizin kazanci"
    },
    {
      icon: Star,
      value: "24/7",
      label: "Destek Hizmeti",
      description: "Kesintisiz yardim"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentData = testimonials[currentTestimonial];

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-b from-primary/5 to-background">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="inline-flex items-center gap-2 rounded-full border border-primary/30 bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6">
              <Star className="h-4 w-4 fill-current" />
              <span>Musteri Hikayeleri</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-600">Gercek Sonuclar</span> ve Basari Hikayeleri
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Sportiva ile isletmelerini donusturen salon sahiplerinin deneyimlerini kesfedin.
            </p>
          </div>

          <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <Card key={index} className="bg-white/60 backdrop-blur-sm border-white/20 text-center hover:bg-white/70 transition-all hover:scale-105">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <div className="text-2xl md:text-3xl font-bold text-primary mb-1">{stat.value}</div>
                    <div className="font-semibold text-foreground mb-1">{stat.label}</div>
                    <div className="text-sm text-muted-foreground">{stat.description}</div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className={`mb-16 transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Card className="bg-gradient-to-br from-white/80 to-white/60 backdrop-blur-sm border-white/20 overflow-hidden">
              <CardContent className="p-8 md:p-12">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                  <div className="lg:col-span-2">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="relative">
                        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                          <Users className="h-8 w-8 text-primary" />
                        </div>
                        <Badge className="absolute -top-2 -right-2 bg-gradient-to-r from-primary to-blue-600 text-white text-xs px-2 py-1">
                          {currentData.plan}
                        </Badge>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-foreground">{currentData.name}</h3>
                        <p className="text-muted-foreground">{currentData.role} • {currentData.gym}</p>
                        <div className="flex items-center gap-1 mt-1">
                          {[...Array(currentData.rating)].map((_, i) => (
                            <Star key={i} className="h-4 w-4 fill-current text-yellow-400" />
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <Quote className="absolute -top-2 -left-2 h-8 w-8 text-primary/20" />
                      <blockquote className="text-lg md:text-xl text-foreground leading-relaxed pl-6">
                      &quot;{currentData.content}&quot;
                      </blockquote>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold text-foreground mb-4">Elde Edilen Sonuclar:</h4>
                    <div className="space-y-3">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="text-lg font-bold text-green-700">{currentData.metrics.memberIncrease}</div>
                        <div className="text-sm text-green-600">Uye Artisi</div>
                      </div>
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <div className="text-lg font-bold text-blue-700">{currentData.metrics.revenueGrowth}</div>
                        <div className="text-sm text-blue-600">Aylik Gelir Artisi</div>
                      </div>
                      <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
                        <div className="text-lg font-bold text-purple-700">{currentData.metrics.timeSaved}</div>
                        <div className="text-sm text-purple-600">Zaman Tasarrufu</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-8 pt-6 border-t border-white/20">
                  <div className="flex items-center gap-2">
                    {testimonials.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentTestimonial(index)}
                        className={`w-2 h-2 rounded-full transition-all ${
                          index === currentTestimonial ? 'bg-primary w-6' : 'bg-primary/30'
                        }`}
                      />
                    ))}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={prevTestimonial}
                      className="bg-white/60 border-white/30"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={nextTestimonial}
                      className="bg-white/60 border-white/30"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            {testimonials.slice(1, 4).map((testimonial) => (
              <Card key={testimonial.id} className="bg-white/60 backdrop-blur-sm border-white/20 hover:bg-white/70 transition-all hover:scale-105">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                      <p className="text-sm text-muted-foreground">{testimonial.gym}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1 mb-3">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-3 w-3 fill-current text-yellow-400" />
                    ))}
                  </div>
                  
                  <p className="text-sm text-foreground leading-relaxed">
                  &quot;{testimonial.content.substring(0, 120)}...&quot;
                  </p>
                  
                  <div className="mt-4 pt-4 border-t border-white/20">
                    <div className="text-sm text-primary font-medium">
                      {testimonial.metrics.memberIncrease} uye artisi • {testimonial.metrics.timeSaved} tasarruf
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}