/**
 * Rating validation and utility functions
 * Following Clean Code principles - focused, single-responsibility functions
 */

import { 
  ValidationResult, 
  RATING_CONSTRAINTS,
  UserRating,
  RatingFormData 
} from '@/types/features/rating';

// Re-export for convenience
export { RATING_CONSTRAINTS };

// ============================================================================
// VALIDATION FUNCTIONS
// ============================================================================

/**
 * Validate rating value (1-5 scale)
 */
export function validateRating(rating: number): ValidationResult {
  if (!rating || typeof rating !== 'number') {
    return { 
      isValid: false, 
      error: 'Puan vermeniz gereklidir' 
    };
  }

  if (rating < RATING_CONSTRAINTS.MIN_RATING || rating > RATING_CONSTRAINTS.MAX_RATING) {
    return { 
      isValid: false, 
      error: `Puan ${RATING_CONSTRAINTS.MIN_RATING}-${RATING_CONSTRAINTS.MAX_RATING} arasında olmalıdır` 
    };
  }

  return { isValid: true };
}

/**
 * Validate rating comment
 */
export function validateComment(comment?: string): ValidationResult {
  if (!comment) {
    return { isValid: true }; // Comments are optional
  }

  const trimmedComment = comment.trim();

  if (trimmedComment.length > RATING_CONSTRAINTS.MAX_COMMENT_LENGTH) {
    return { 
      isValid: false, 
      error: `Yorum en fazla ${RATING_CONSTRAINTS.MAX_COMMENT_LENGTH} karakter olmalıdır` 
    };
  }

  return { isValid: true };
}

/**
 * Validate complete rating form data
 */
export function validateRatingForm(formData: RatingFormData): ValidationResult {
  // Validate rating
  const ratingValidation = validateRating(formData.rating);
  if (!ratingValidation.isValid) {
    return ratingValidation;
  }

  // Validate comment
  const commentValidation = validateComment(formData.comment);
  if (!commentValidation.isValid) {
    return commentValidation;
  }

  return { isValid: true };
}

/**
 * Validate membership status for rating eligibility
 */
export function validateMembershipForRating(
  membershipStatus: 'none' | 'pending' | 'active' | 'suspended'
): ValidationResult {
  if (membershipStatus !== 'active') {
    return {
      isValid: false,
      error: 'Salon değerlendirmesi yapabilmek için aktif üyeliğiniz olması gerekir'
    };
  }

  return { isValid: true };
}

// ============================================================================
// DATA TRANSFORMATION FUNCTIONS
// ============================================================================

/**
 * Create rating record data for database insertion
 */
export function createRatingRecord(
  userId: string,
  gymId: string,
  formData: RatingFormData
): Omit<UserRating, 'id' | 'created_at' | 'updated_at'> {
  return {
    profile_id: userId,
    gym_id: gymId,
    rating: formData.rating,
    comment: formData.comment?.trim() || null,
  };
}

/**
 * Format rating for display
 */
export function formatRating(rating: number): string {
  if (!rating || rating < RATING_CONSTRAINTS.MIN_RATING || rating > RATING_CONSTRAINTS.MAX_RATING) {
    return '0.0';
  }
  return rating.toFixed(1);
}

/**
 * Format date for rating display
 */
export function formatRatingDate(dateString: string): string {
  if (!dateString) return '';

  const date = new Date(dateString);
  return date.toLocaleDateString('tr-TR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

// ============================================================================
// STATISTICS CALCULATION FUNCTIONS
// ============================================================================

/**
 * Calculate rating statistics from reviews array
 */
export function calculateRatingStats(ratings: UserRating[]): {
  averageRating: number;
  totalRatings: number;
  ratingDistribution: Record<string, number>;
} {
  if (!Array.isArray(ratings) || ratings.length === 0) {
    return {
      averageRating: 0,
      totalRatings: 0,
      ratingDistribution: { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 },
    };
  }

  const validRatings = ratings.filter(
    rating => 
      rating.rating && 
      typeof rating.rating === 'number' &&
      rating.rating >= RATING_CONSTRAINTS.MIN_RATING &&
      rating.rating <= RATING_CONSTRAINTS.MAX_RATING
  );

  if (validRatings.length === 0) {
    return {
      averageRating: 0,
      totalRatings: ratings.length,
      ratingDistribution: { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 },
    };
  }

  // Calculate average
  const totalRating = validRatings.reduce((sum, rating) => sum + rating.rating, 0);
  const averageRating = totalRating / validRatings.length;

  // Calculate distribution
  const distribution: Record<string, number> = { '1': 0, '2': 0, '3': 0, '4': 0, '5': 0 };
  validRatings.forEach(rating => {
    distribution[rating.rating.toString()]++;
  });

  return {
    averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
    totalRatings: ratings.length,
    ratingDistribution: distribution,
  };
}

// ============================================================================
// UI HELPER FUNCTIONS
// ============================================================================

/**
 * Get rating label text for display
 */
export function getRatingLabel(rating: number): string {
  const labels: Record<number, string> = {
    1: 'Çok Kötü',
    2: 'Kötü',
    3: 'Orta',
    4: 'İyi',
    5: 'Mükemmel',
  };

  return labels[rating] || '';
}

/**
 * Get rating color class based on rating value
 */
export function getRatingColorClass(rating: number): string {
  if (rating >= 4.5) return 'text-green-600';
  if (rating >= 3.5) return 'text-yellow-600';
  if (rating >= 2.5) return 'text-orange-600';
  return 'text-red-600';
}

/**
 * Check if user can rate (has active membership and no existing rating)
 */
export function canUserRate(
  membershipStatus: 'none' | 'pending' | 'active' | 'suspended',
  existingRating?: UserRating
): { canRate: boolean; reason?: string } {
  // Check membership status
  const membershipValidation = validateMembershipForRating(membershipStatus);
  if (!membershipValidation.isValid) {
    return { 
      canRate: false, 
      reason: membershipValidation.error 
    };
  }

  // Check if user already has a rating
  if (existingRating) {
    return { 
      canRate: false, 
      reason: 'Bu salon için zaten bir değerlendirmeniz bulunmaktadır' 
    };
  }

  return { canRate: true };
}

/**
 * Generate success message for rating operations
 */
export function getRatingSuccessMessage(operation: 'create' | 'update' | 'delete'): string {
  const messages = {
    create: 'Değerlendirmeniz başarıyla kaydedildi',
    update: 'Değerlendirmeniz başarıyla güncellendi',
    delete: 'Değerlendirmeniz başarıyla silindi',
  };

  return messages[operation];
}