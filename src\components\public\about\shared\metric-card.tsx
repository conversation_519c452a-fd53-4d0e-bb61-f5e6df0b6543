"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AnimatedCounter } from "./animated-counter";
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface MetricCardProps {
  title: string;
  value: number;
  suffix?: string;
  prefix?: string;
  description: string;
  icon: LucideIcon;
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
  };
  details?: string;
  category: "growth" | "performance" | "usage" | "satisfaction";
  className?: string;
}

const categoryColors = {
  growth: "text-green-600 bg-green-50 border-green-200",
  performance: "text-blue-600 bg-blue-50 border-blue-200", 
  usage: "text-purple-600 bg-purple-50 border-purple-200",
  satisfaction: "text-orange-600 bg-orange-50 border-orange-200",
};

export function MetricCard({
  title,
  value,
  suffix = "",
  prefix = "",
  description,
  icon: Icon,
  trend,
  details,
  category,
  className,
}: MetricCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card 
      className={cn(
        "relative overflow-hidden transition-all duration-300 cursor-pointer group",
        "hover:shadow-lg hover:scale-105",
        categoryColors[category],
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardContent className="p-6">
        {/* Header with Icon */}
        <div className="flex items-center justify-between mb-4">
          <div className={cn(
            "w-12 h-12 rounded-lg flex items-center justify-center transition-all duration-300",
            category === "growth" && "bg-green-100 text-green-600",
            category === "performance" && "bg-blue-100 text-blue-600",
            category === "usage" && "bg-purple-100 text-purple-600",
            category === "satisfaction" && "bg-orange-100 text-orange-600",
            isHovered && "scale-110"
          )}>
            <Icon className="h-6 w-6" />
          </div>
          
          {trend && (
            <Badge 
              variant={trend.isPositive ? "default" : "destructive"}
              className="text-xs"
            >
              {trend.isPositive ? "+" : ""}{trend.value}% {trend.label}
            </Badge>
          )}
        </div>

        {/* Main Metric */}
        <div className="space-y-2 mb-4">
          <div className="text-3xl font-bold">
            <AnimatedCounter
              value={value}
              prefix={prefix}
              suffix={suffix}
              duration={2500}
            />
          </div>
          <h3 className="font-semibold text-lg">{title}</h3>
          <p className="text-muted-foreground text-sm">{description}</p>
        </div>

        {/* Detailed Information - Shows on Hover */}
        {details && (
          <div className={cn(
            "absolute inset-0 bg-background/95 backdrop-blur-sm p-6 flex flex-col justify-center",
            "transition-all duration-300",
            isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-full"
          )}>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Icon className="h-5 w-5" />
                <span className="font-semibold">{title}</span>
              </div>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {details}
              </p>
              {trend && (
                <div className="flex items-center text-xs">
                  <span className="font-medium">Son 30 gün: </span>
                  <span className={cn(
                    "ml-1",
                    trend.isPositive ? "text-green-600" : "text-red-600"
                  )}>
                    {trend.isPositive ? "↗" : "↘"} {trend.value}% {trend.label}
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-muted h-1 rounded-full overflow-hidden">
            <div 
              className={cn(
                "h-full transition-all duration-1000 ease-out",
                category === "growth" && "bg-green-500",
                category === "performance" && "bg-blue-500",
                category === "usage" && "bg-purple-500",
                category === "satisfaction" && "bg-orange-500"
              )}
              style={{ 
                width: isHovered ? "100%" : `${Math.min((value / 1000) * 100, 100)}%` 
              }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}