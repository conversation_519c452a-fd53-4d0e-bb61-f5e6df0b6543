'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { logger } from '@/lib/logger';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    logger.error('Global app error', error, { digest: (error as any)?.digest });
  }, [error]);

  return (
    <html>
      <body>
        <div className="flex min-h-screen items-center justify-center p-6">
          <div className="mx-auto w-full max-w-lg space-y-4 text-center">
            <div className="border-destructive/30 bg-destructive/5 rounded-lg border p-6">
              <div className="text-destructive mb-2 flex items-center justify-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                <h2 className="text-xl font-semibold"><PERSON><PERSON> şeyler ters gitti</h2>
              </div>
              <p className="text-muted-foreground text-sm">
                Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.
              </p>
            </div>
            <div className="flex justify-center gap-2">
              <Button variant="outline" size="sm" onClick={reset}>
                <RefreshCw className="mr-2 h-4 w-4" /> Tekrar Dene
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href="/">
                  <Home className="mr-2 h-4 w-4" /> Ana Sayfa
                </Link>
              </Button>
            </div>
            {process.env.NODE_ENV === 'development' && (
              <details className="mx-auto max-w-lg text-left">
                <summary className="text-muted-foreground cursor-pointer text-xs">
                  Hata detayları (geliştirici modu)
                </summary>
                <pre className="bg-muted/30 mt-2 overflow-auto rounded-md border p-3 text-[10px] leading-snug">
                  {error.message}
                  {'\n'}
                  {((error as any)?.digest &&
                    `Digest: ${(error as any).digest}`) ||
                    ''}
                </pre>
              </details>
            )}
          </div>
        </div>
      </body>
    </html>
  );
}
