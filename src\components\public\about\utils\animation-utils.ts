export interface ScrollAnimation {
  trigger: string;
  animation: 'fadeIn' | 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'countUp' | 'progressFill' | 'scale';
  duration: number;
  delay?: number;
  easing?: string;
}

export const animationPresets = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.6, ease: "easeOut" }
  },
  slideUp: {
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  },
  slideDown: {
    initial: { opacity: 0, y: -50 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  },
  slideLeft: {
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  },
  slideRight: {
    initial: { opacity: 0, x: -50 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.6, ease: "easeOut" }
  },
  scale: {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.6, ease: "easeOut" }
  }
};

export const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

export const staggerItem = {
  initial: { opacity: 0, y: 20 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.5, ease: "easeOut" }
  }
};

export function getAnimationClass(animation: ScrollAnimation['animation']): string {
  const baseClasses = "transition-all duration-300 ease-out";
  
  switch (animation) {
    case 'fadeIn':
      return `${baseClasses} opacity-0 data-[intersecting=true]:opacity-100`;
    case 'slideUp':
      return `${baseClasses} opacity-0 translate-y-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-y-0`;
    case 'slideDown':
      return `${baseClasses} opacity-0 -translate-y-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-y-0`;
    case 'slideLeft':
      return `${baseClasses} opacity-0 translate-x-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-x-0`;
    case 'slideRight':
      return `${baseClasses} opacity-0 -translate-x-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-x-0`;
    case 'scale':
      return `${baseClasses} opacity-0 scale-95 data-[intersecting=true]:opacity-100 data-[intersecting=true]:scale-100`;
    default:
      return baseClasses;
  }
}

export function createScrollTrigger(
  element: HTMLElement,
  animation: ScrollAnimation,
  callback?: () => void
): IntersectionObserver {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.setAttribute('data-intersecting', 'true');
          
          if (callback) {
            setTimeout(callback, animation.delay || 0);
          }
          
          // Unobserve after animation to improve performance
          observer.unobserve(entry.target);
        }
      });
    },
    {
      threshold: 0.1,
      rootMargin: '0px 0px -10% 0px'
    }
  );
  
  observer.observe(element);
  return observer;
}

// Utility for reduced motion preferences
export function shouldReduceMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

// Performance-optimized easing functions
export const easingFunctions = {
  easeInOut: (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
  easeOut: (t: number) => t * (2 - t),
  easeIn: (t: number) => t * t,
  easeOutQuart: (t: number) => 1 - Math.pow(1 - t, 4),
  easeOutCubic: (t: number) => 1 - Math.pow(1 - t, 3),
  easeOutElastic: (t: number) => {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : Math.pow(2, -10 * t) * Math.sin((t * 10 - 0.75) * c4) + 1;
  }
};

// Debounce function for scroll events
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): T {
  let timeout: NodeJS.Timeout;
  
  return ((...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T;
}