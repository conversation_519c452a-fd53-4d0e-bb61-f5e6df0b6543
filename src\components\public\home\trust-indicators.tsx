'use client';

import { AnimatedSection, AnimatedCounter } from '@/components/ui/animated-section';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Building2,
  Calendar,
  TrendingUp,
  Award,
  Clock
} from 'lucide-react';

export function TrustIndicators() {
  const stats = [
    {
      icon: Users,
      value: 15000,
      suffix: '+',
      label: 'Aktif <PERSON>',
      description: 'Platform üzerinde kayıtlı üye sayısı'
    },
    {
      icon: Building2,
      value: 150,
      suffix: '+',
      label: 'Spor Salonu',
      description: 'Türkiye genelinde partner salon'
    },
    {
      icon: Calendar,
      value: 50000,
      suffix: '+',
      label: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Başarıyla gerçekleşen rezervasyon'
    },
    {
      icon: TrendingUp,
      value: 98,
      suffix: '%',
      label: 'Müşteri Memnuniyeti',
      description: 'Kullanıcı geri bildirimlerine göre'
    }
  ];

  const badges = [
    {
      icon: Award,
      text: 'KVKV Uyumlu',
      variant: 'outline' as const
    },
    {
      icon: Clock,
      text: '7/24 Destek',
      variant: 'outline' as const
    },
    {
      icon: TrendingUp,
      text: 'ISO 27001',
      variant: 'outline' as const
    }
  ];

  return (
    <section className="relative py-16 lg:py-20 bg-gradient-to-br from-muted/30 via-background to-muted/20">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-20" />
      
      <div className="container relative mx-auto px-4">
        <div className="mx-auto max-w-7xl">
          {/* Trust badges */}
          <AnimatedSection animation="fade-up" delay={100}>
            <div className="mb-16 text-center">
              <p className="mb-6 text-sm text-muted-foreground">
                Güvenilir, güvenli ve sertifikalı platform
              </p>
              <div className="flex flex-wrap items-center justify-center gap-4">
                {badges.map((badge, index) => {
                  const IconComponent = badge.icon;
                  return (
                    <Badge
                      key={index}
                      variant={badge.variant}
                      className="flex items-center gap-2 px-4 py-2 text-sm border-primary/20 bg-background/80 backdrop-blur"
                    >
                      <IconComponent className="h-4 w-4" />
                      {badge.text}
                    </Badge>
                  );
                })}
              </div>
            </div>
          </AnimatedSection>

          {/* Stats grid */}
          <div className="grid grid-cols-2 gap-6 md:grid-cols-4">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <AnimatedSection
                  key={index}
                  animation="fade-up"
                  delay={200 + (index * 100)}
                >
                  <div className="group text-center p-6 rounded-2xl border border-primary/10 bg-card/50 backdrop-blur transition-all duration-300 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1">
                    <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-2xl bg-primary/10 group-hover:bg-primary/20 transition-colors">
                      <IconComponent className="h-8 w-8 text-primary" />
                    </div>
                    
                    <div className="mb-2">
                      <AnimatedCounter
                        target={stat.value}
                        suffix={stat.suffix}
                        className="text-3xl font-bold text-foreground md:text-4xl"
                        duration={2500}
                      />
                    </div>
                    
                    <h3 className="mb-2 text-lg font-semibold text-foreground">
                      {stat.label}
                    </h3>
                    
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {stat.description}
                    </p>
                  </div>
                </AnimatedSection>
              );
            })}
          </div>

          {/* Bottom section with additional trust elements */}
          <AnimatedSection animation="fade-up" delay={800}>
            <div className="mt-16 text-center">
              <div className="mx-auto max-w-4xl">
                <p className="text-lg text-muted-foreground mb-8">
                  <span className="font-semibold text-primary">2020&apos;den bu yana</span> spor salonu yönetiminde dijital dönüşümün lideri olarak, 
                  <span className="font-semibold text-foreground"> binlerce işletmeye</span> hizmet veriyoruz.
                </p>
                
                <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
                  <div className="text-sm font-medium">Kullanıldığı sektörler:</div>
                  <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                    <span>Fitness</span>
                    <span>•</span>
                    <span>CrossFit</span>
                    <span>•</span>
                    <span>Pilates</span>
                    <span>•</span>
                    <span>Yoga</span>
                    <span>•</span>
                    <span>Yüzme</span>
                    <span>•</span>
                    <span>Dövüş Sporları</span>
                  </div>
                </div>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  );
}