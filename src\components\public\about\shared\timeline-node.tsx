"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface TimelineNodeProps {
  year: string;
  title: string;
  description: string;
  details: string;
  image?: string;
  achievements?: string[];
  isActive?: boolean;
  side?: "left" | "right";
}

export function TimelineNode({
  year,
  title,
  description,
  details,
  image,
  achievements = [],
  isActive = false,
  side = "left",
}: TimelineNodeProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className={cn(
      "relative flex items-center mb-12",
      side === "right" ? "flex-row-reverse" : "flex-row"
    )}>
      {/* Timeline line connector */}
      <div className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-border -translate-x-px hidden lg:block" />
      
      {/* Timeline node circle */}
      <div className={cn(
        "absolute left-1/2 top-8 w-4 h-4 rounded-full border-4 border-background -translate-x-1/2 hidden lg:block z-10",
        isActive ? "bg-primary" : "bg-muted"
      )} />

      {/* Content card */}
      <Card className={cn(
        "w-full lg:w-96 transition-all duration-300 hover:shadow-lg",
        side === "right" ? "lg:mr-8" : "lg:ml-8",
        isActive && "ring-2 ring-primary/20"
      )}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Badge variant={isActive ? "default" : "secondary"} className="w-fit">
              {year}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 h-auto"
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
          <CardTitle className="text-xl">{title}</CardTitle>
          <CardDescription className="text-base">{description}</CardDescription>
        </CardHeader>
        
        {isExpanded && (
          <CardContent className="pt-0 space-y-4">
            {image && (
              <div className="w-full h-32 bg-muted rounded-lg flex items-center justify-center">
                <span className="text-muted-foreground text-sm">Timeline Image</span>
              </div>
            )}
            
            <p className="text-sm text-muted-foreground leading-relaxed">
              {details}
            </p>
            
            {achievements.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Başlıca Başarılar:</h4>
                <ul className="space-y-1">
                  {achievements.map((achievement, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-start">
                      <span className="w-1.5 h-1.5 bg-primary rounded-full mt-2 mr-2 flex-shrink-0" />
                      {achievement}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  );
}