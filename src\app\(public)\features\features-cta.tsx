'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Crown,
  ArrowRight,
  CheckCircle,
  Star,
  Zap,
  Users,
  Shield,
  Target,
  PlayCircle,
  Clock,
  Lightbulb,
} from 'lucide-react';
import Link from 'next/link';
import { useMemo } from 'react';

export function FeaturesCTA() {
  // Onboarding steps
  const onboardingSteps = useMemo(() => [
    {
      step: 1,
      title: 'He<PERSON><PERSON>',
      description: '2 dakikada hızlı kayıt',
      duration: '2 dk',
      icon: Users
    },
    {
      step: 2,
      title: 'Salon Bilgilerini Girme',
      description: 'Salon detayları ve tercihleri',
      duration: '5 dk',
      icon: Target
    },
    {
      step: 3,
      title: 'Üye Aktarma',
      description: 'Mevcut üyeleri içe aktarma',
      duration: '10 dk',
      icon: ArrowRight
    },
    {
      step: 4,
      title: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'İlk işlemleri gerçekleştirme',
      duration: '15 dk',
      icon: CheckCircle
    }
  ], []);

  const features = useMemo(() => [
    'Sınırsız üye kaydı',
    'Gelişmiş analitik raporlar',
    'Mobil uygulama desteği',
    '7/24 teknik destek',
    'Özelleştirilebilir arayüz',
    'Güvenli ödeme sistemi',
  ], []);

  return (
    <section className="from-primary/5 via-background to-primary/5 relative overflow-hidden bg-gradient-to-br py-20 lg:py-32">
      {/* Enhanced Background Elements */}
      <div className="bg-grid-pattern absolute inset-0 opacity-5"></div>
      <div className="bg-primary/10 absolute top-10 right-10 h-64 w-64 rounded-full blur-3xl animate-pulse"></div>
      <div className="bg-primary/5 absolute bottom-10 left-10 h-80 w-80 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="bg-secondary/10 absolute top-1/2 left-1/3 h-48 w-48 rounded-full blur-2xl animate-pulse delay-500"></div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="mx-auto max-w-7xl">
          {/* Main CTA Header */}
          <div className="text-center mb-16">
            <Badge
              variant="outline"
              className="mb-6 px-6 py-3 text-sm font-medium bg-background/50 backdrop-blur-sm border-primary/20"
            >
              <Zap className="mr-2 h-4 w-4 animate-pulse" />
              Dijital Dönüşümü Başlatın
            </Badge>

            <h2 className="text-primary mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
              Spor Salonu İşletmeciliğinde
              <span className="block bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Yeni Çağ Başlıyor
              </span>
            </h2>

            <p className="text-muted-foreground mx-auto mb-8 max-w-3xl text-lg leading-relaxed">
              Sportiva ile işletmenizi modernleştirin, verimliliği artırın ve rekabette öne geçin. 
              <span className="text-primary font-semibold">Hemen başlayın ve farkı yaşayın!</span>
            </p>
          </div>

          {/* Onboarding Preview */}
          <div className="mb-12">
            <Card className="bg-background/80 backdrop-blur-sm border-border/50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-6 w-6 text-primary" />
                  Sadece 30 Dakikada Kurulum
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {onboardingSteps.map((step, index) => {
                    const IconComponent = step.icon;
                    return (
                      <div key={step.step} className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-primary-foreground font-bold">
                            {step.step}
                          </div>
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold flex items-center gap-2">
                              <IconComponent className="h-5 w-5 text-primary" />
                              {step.title}
                            </h4>
                            <Badge variant="outline" className="text-xs">
                              <Clock className="mr-1 h-3 w-3" />
                              {step.duration}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">{step.description}</p>
                        </div>
                        
                        {index < onboardingSteps.length - 1 && (
                          <div className="absolute left-5 mt-12 w-0.5 h-8 bg-border"></div>
                        )}
                      </div>
                    );
                  })}
                </div>
                
                <div className="mt-8 p-6 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl">
                  <div className="text-center">
                    <h4 className="font-semibold mb-2">Hazır mısınız?</h4>
                    <p className="text-sm text-muted-foreground mb-4">
                      Ücretsiz deneme sürecinizi başlatın ve 30 dakika içinde salonunuzu dijitalleştirin
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button asChild size="lg">
                        <Link href="/auth/register">
                          <PlayCircle className="mr-2 h-5 w-5" />
                          Kurulumu Başlat
                        </Link>
                      </Button>
                      <Button variant="outline" asChild>
                        <Link href="/onboarding">
                          Demo İzle
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Features Showcase */}
          <div className="bg-muted/20 border-border rounded-3xl border p-8 md:p-12 backdrop-blur-sm">
            <div className="text-center mb-12">
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium">
                <Star className="mr-2 h-4 w-4" />
                Öne Çıkan Özellikler
              </Badge>
              <h3 className="text-primary mb-4 text-2xl font-bold md:text-3xl">
                Sportiva ile Elde Edeceğiniz
                <span className="block bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  Rekabetsel Avantajlar
                </span>
              </h3>
              <p className="text-muted-foreground mx-auto max-w-3xl text-lg">
                Sektörün en gelişmiş özellikleri ile işletmenizi modernleştirin
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <div key={index} className="group">
                  <div className="bg-background/50 rounded-xl p-6 border border-border/50 hover:border-primary/30 transition-all duration-300 hover:shadow-lg">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="bg-primary/10 p-2 rounded-lg group-hover:bg-primary/20 transition-colors">
                        <CheckCircle className="text-primary h-5 w-5" />
                      </div>
                      <span className="font-semibold text-foreground">{feature}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-12 text-center">
              <div className="bg-gradient-to-r from-primary/5 to-secondary/5 rounded-2xl p-8 mb-8">
                <h4 className="font-bold text-xl mb-4">💪 Hemen başlayın!</h4>
                <p className="text-muted-foreground mb-6">
                  30 gün ücretsiz deneme ile tüm özellikleri keşfedin. Kredi kartı gerekmez!
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="shadow-xl">
                    <Link href="/auth/register">
                      <Crown className="mr-2 h-5 w-5" />
                      Ücretsiz Başlayın
                    </Link>
                  </Button>
                  <Button variant="outline" asChild size="lg">
                    <Link href="/findGym">
                      Salonları Keşfedin
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
              
              {/* Trust Indicators */}
              <div className="flex flex-wrap items-center justify-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Shield className="text-primary h-4 w-4" />
                  <span>SSL Şifreleme</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="text-primary h-4 w-4" />
                  <span>7/24 Türkçe Destek</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="text-primary h-4 w-4" />
                  <span>KVKK Uyumlu</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="text-primary h-4 w-4" />
                  <span>%99.9 Uptime</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
