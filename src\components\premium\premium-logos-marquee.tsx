'use client';

import { AnimatedSection } from '@/components/ui/animated-section';
import { Badge } from '@/components/ui/badge';
import { Star, Award, Building2 } from 'lucide-react';

export function PremiumLogosMarquee() {
  const brands = [
    { name: 'FitLife', category: 'Fitness Zinciri' },
    { name: 'PowerGym', category: 'Ağırlı<PERSON> Antrenmanı' },
    { name: 'FlexStudio', category: 'Yoga & Pilates' },
    { name: 'Elite Fitness', category: 'Premium Klub' },
    { name: 'BodyForge', category: 'CrossFit' },
    { name: 'AquaFit', category: 'Yüzme & Su Sporları' },
    { name: 'MoveWell', category: 'Rehabilitasyon' },
    { name: 'StrongZone', category: '<PERSON><PERSON><PERSON>' },
    { name: 'FlexiGym', category: 'Genel Fitness' },
    { name: 'VitalSport', category: 'Sağlık & Wellness' }
  ];

  const stats = [
    {
      icon: Building2,
      value: '180+',
      label: 'Spor Salonu',
      description: 'Türk<PERSON>ye genelinde'
    },
    {
      icon: Star,
      value: '4.9/5',
      label: 'Müşteri Memnuniyeti',
      description: '500+ değerlendirme'
    },
    {
      icon: Award,
      value: '%96',
      label: 'Tavsiye Oranı',
      description: 'Müşteriler tarafından'
    }
  ];

  return (
    <section className="py-20 lg:py-28 bg-gradient-to-br from-muted/20 via-background to-muted/30">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10" />
      
      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <AnimatedSection animation="fade-up" delay={100}>
          <div className="mx-auto mb-16 max-w-3xl text-center">
            <Badge 
              variant="outline" 
              className="mb-4 px-4 py-2 border-primary/20 bg-primary/10 text-primary"
            >
              <Award className="mr-2 h-4 w-4" />
              Güvenilir Ortaklar
            </Badge>
            
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                Türkiye&apos;nin Önde Gelen
              </span>
              <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Spor Salonları Tercih Ediyor
              </span>
            </h2>
            
            <p className="text-xl text-muted-foreground leading-relaxed">
              Fitness&apos;tan yoga&apos;ya, CrossFit&apos;ten rehabilitasyona kadar her tür spor merkezinde 
              güvenle kullanılıyor.
            </p>
          </div>
        </AnimatedSection>

        {/* Stats Grid */}
        <AnimatedSection animation="fade-up" delay={200}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16 max-w-4xl mx-auto">
            {stats.map((stat, _index) => {
              const IconComponent = stat.icon;
              return (
                <div
                  key={stat.label}
                  className="text-center p-6 rounded-xl border border-border/50 bg-card/80 backdrop-blur transition-all duration-300 hover:border-primary/30 hover:shadow-lg"
                >
                  <div className="mx-auto mb-4 w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center">
                    <IconComponent className="h-8 w-8 text-primary" />
                  </div>
                  
                  <div className="text-3xl font-bold text-primary mb-2">
                    {stat.value}
                  </div>
                  
                  <h3 className="text-lg font-semibold text-foreground mb-1">
                    {stat.label}
                  </h3>
                  
                  <p className="text-sm text-muted-foreground">
                    {stat.description}
                  </p>
                </div>
              );
            })}
          </div>
        </AnimatedSection>

        {/* Logos Marquee */}
        <AnimatedSection animation="fade-up" delay={400}>
          <div className="mb-8 text-center">
            <h3 className="text-2xl font-bold text-foreground mb-2">
              Bize Güvenen Markalar
            </h3>
            <p className="text-muted-foreground">
              Çeşitli sektörlerden spor merkezleri Sportiva ile büyüyor
            </p>
          </div>
          
          <div className="relative overflow-hidden [mask-image:linear-gradient(to_right,transparent,black_10%,black_90%,transparent)]">
            <div className="animate-scroll flex w-max gap-12 whitespace-nowrap">
              {[...brands, ...brands].map((brand, i) => (
                <div
                  key={`${brand.name}-${i}`}
                  className="group flex flex-col items-center justify-center rounded-2xl border border-border/50 bg-gradient-to-br from-background/90 to-background/70 p-8 shadow-sm backdrop-blur transition-all duration-500 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1 min-w-[200px]"
                >
                  <div className="mb-3">
                    <span className="text-2xl font-bold tracking-wide text-foreground/90 group-hover:text-primary transition-colors">
                      {brand.name}
                    </span>
                  </div>
                  
                  <div className="text-xs text-muted-foreground font-medium">
                    {brand.category}
                  </div>
                  
                  {/* Decorative element */}
                  <div className="mt-4 h-1 w-12 bg-gradient-to-r from-primary/30 to-primary/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              ))}
            </div>
          </div>
        </AnimatedSection>

        {/* Bottom Section */}
        <AnimatedSection animation="fade-up" delay={600}>
          <div className="text-center mt-16">
            <div className="inline-flex items-center gap-2 rounded-full border border-green-200/50 bg-green-500/10 px-6 py-3 text-sm font-medium text-green-700">
              <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
              <span>Her gün yeni ortaklıklar ekleniyor</span>
            </div>
            
            <p className="mt-4 text-muted-foreground">
              Siz de bu başarılı markalara katılın ve dijital dönüşümünüzü başlatın.
            </p>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
