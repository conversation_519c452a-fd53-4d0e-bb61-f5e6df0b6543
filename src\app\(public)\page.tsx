import { StructuredData } from '@/components/seo/structured-data';
import { Metadata } from 'next';
import { PricingModern } from '@/components/public/home/<USER>';
import { PremiumStats } from '@/components/premium/premium-stats'
import { PremiumShowcase } from '@/components/premium/premium-showcase'
import { PremiumTestimonials } from '@/components/premium/premium-testimonials'
import { PremiumCTA } from '@/components/premium/premium-cta'
import { PremiumLogosMarquee } from '@/components/premium/premium-logos-marquee'
import { RoleFeatures } from '@/components/public/home/<USER>'
import { AboutFeatures } from '@/components/public/about/about-features'
import { ModernHero } from '@/components/public/home/<USER>'
import { TrustIndicators } from '@/components/public/home/<USER>'
import { ProcessFlow } from '@/components/public/home/<USER>'
export const metadata: Metadata = {
  title: 'Sportiva | Türkiye\'nin #1 Dijital Spor Salonu Platformu',
  description:
    'Modern spor salonu yönetimi için tasarlanmış akıllı platform. Üye yönetimi, randevu sistemi, analitik raporlar ve daha fazlası. Ücretsiz deneyin!',
  keywords: [
    'spor salonu yönetimi',
    'fitness yönetim sistemi',
    'salon üye takibi',
    'spor salonu yazılımı',
    'dijital salon yönetimi',
    'fitness teknolojisi',
    'salon bul',
    'fitness merkezi',
    'randevu sistemi',
    'üye yönetimi',
    'analitik raporlama',
    'çoklu şube yönetimi'
  ],
  openGraph: {
    title: 'Sportiva | Türkiye\'nin #1 Dijital Spor Salonu Platformu',
    description:
      'Modern spor salonu yönetimi için tasarlanmış akıllı platform. Üye yönetimi, randevu sistemi, analitik raporlar ve daha fazlası.',
    images: ['/placeholder.jpg'],
  },
  alternates: {
    canonical: '/',
  },
};

export default function Home() {
  return (
    <>
      <StructuredData
        type="Product"
        data={{
          name: 'Sportiva',
          description:
            'Modern spor salonu yönetimi için tasarlanmış akıllı platform. Üye yönetimi, randevu sistemi, analitik raporlar ve daha fazlası.',
          brand: 'Sportiva',
          offers: { priceCurrency: 'TRY', availability: 'InStock' },
        }}
      />

      <main className="relative isolate flex-1 overflow-hidden">
        {/* Hero Section with enhanced design */}
        <ModernHero />
        
        {/* Trust Indicators */}
        <TrustIndicators />
        
        {/* Social Proof & Stats */}
        <PremiumStats />
        
        {/* Process Flow */}
        <ProcessFlow />
        
        {/* Core Features */}
        <AboutFeatures />
        
        {/* Role-specific Features */}
        <RoleFeatures />
        
        {/* Success Stories & Testimonials */}
        <PremiumTestimonials />
        
        {/* Product Showcase */}
        <PremiumShowcase />
        
        {/* Partner Logos */}
        <PremiumLogosMarquee />
        
        {/* Pricing */}
        <PricingModern showHeader={true} compact={false} />
        
        {/* Final CTA */}
        <PremiumCTA />
      </main>
    </>
  );
}
