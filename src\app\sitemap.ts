import { MetadataRoute } from 'next';
import { generateCitySlug } from '@/lib/utils/city-utils';
import {
  getSiteUrl,
  getFullUrl,
  getFindGymUrl,
  getSearchUrl,
} from '@/lib/utils/url-utils';
import { CITIES, FEATURE_GROUPS } from '@/lib/constants';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = getSiteUrl();

  // Ana sayfalar
  const mainPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: getFullUrl('/pricing'),
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: getFullUrl('/findGym'),
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    },
    {
      url: getFullUrl('/auth/login'),
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
    {
      url: getFullUrl('/auth/register'),
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.5,
    },
  ];

  // Tüm şehirler için clean URL'ler - popüler mantığı kaldırıldı
  const cityPages = CITIES.slice(0, 30).map(city => ({
    url: getFindGymUrl(generateCitySlug(city.name)),
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.6,
  }));

  // Özellik bazlı salon sayfaları
  const features = Object.values(FEATURE_GROUPS)
    .flatMap(group => group.features)
    .slice(0, 15);
  const featurePages = features.map(feature => ({
    url: getSearchUrl({ features: feature }),
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.5,
  }));

  return [...mainPages, ...cityPages, ...featurePages];
}
