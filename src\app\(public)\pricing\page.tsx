export const dynamic = 'force-static';

import { PricingHero } from '@/components/public/pricing/pricing-hero';
import { PricingCardsModern } from '@/components/public/pricing/pricing-cards-modern';
import { PricingComparison } from '@/components/public/pricing/pricing-comparison';
import { PricingROICalculator } from '@/components/public/pricing/pricing-roi-calculator';
import { PricingTestimonials } from '@/components/public/pricing/pricing-testimonials';
import { PricingFAQ } from '@/components/public/pricing/pricing-faq';
import { PricingCTA } from '@/components/public/pricing/pricing-cta';
import { fetchAllPlatformPackages } from '@/lib/actions/business/platform-packages';

export default async function PricingPage() {
  const res = await fetchAllPlatformPackages();
  const packages = res.data ?? [];
  
  return (
    <main className="flex-1 overflow-hidden">
      {/* Hero Section with improved animations and messaging */}
      <PricingHero />
      
      {/* Modern Pricing Cards with interactive features */}
      <PricingCardsModern packages={packages} />
      
      {/* Interactive ROI Calculator */}
      <PricingROICalculator />
      
      {/* Enhanced Features Comparison */}
      <PricingComparison packages={packages} />
      
      {/* Customer Testimonials and Social Proof */}
      <PricingTestimonials />
      
      {/* Modern FAQ with categorization and search */}
      <PricingFAQ />
      
      {/* Final CTA with urgency elements */}
      <PricingCTA />
    </main>
  );
}
