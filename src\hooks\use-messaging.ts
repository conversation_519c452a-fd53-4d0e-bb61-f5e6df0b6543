'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

// Basitleştirilmiş mesajlaşma tipleri - sadece birebir sohbetler
interface ChatMessage {
  id: string;
  chat_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  updated_at?: string;
  sender?: {
    id: string;
    email: string;
    full_name?: string;
  };
}

interface Chat {
  id: string;
  user1_id: string;
  user2_id: string;
  created_at: string;
  updated_at: string;
  other_user?: {
    id: string;
    email: string;
    full_name?: string;
  };
  last_message?: {
    content: string;
    created_at: string;
  };
}

interface UseMessagingReturn {
  conversations: Chat[];
  activeConversation: Chat | null;
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  currentUser: any;
  setActiveConversation: (chat: Chat | null) => void;
  sendMessage: (content: string) => Promise<void>;
  createConversation: (otherUserId: string) => Promise<string | undefined>;
  loadConversations: () => Promise<void>;
}

export function useMessaging(): UseMessagingReturn {
  const [conversations, setConversations] = useState<Chat[]>([]);
  const [activeConversation, setActiveConversation] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);

  const supabase = createClient();
  const subscriptionRef = useRef<any>(null);

  // Kullanıcı bilgisini al
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
    };
    getUser();
  }, [supabase]);

  // Chat'leri yükle
  const loadConversations = useCallback(async () => {
    if (!currentUser) return;

    try {
      setIsLoading(true);

      // Yeni basit chat sistemini kullan
      const { data, error } = await supabase.rpc('get_user_chats');

      if (error) {
        console.error('Chat\'ler alınırken hata:', error);
        setError('Chat\'ler yüklenemedi');
        return;
      }

      // Veriyi Chat tipine dönüştür
      const chats: Chat[] = (data || []).map((row: any) => ({
        id: row.chat_id,
        user1_id: currentUser.id,
        user2_id: row.other_user_id,
        created_at: row.updated_at,
        updated_at: row.updated_at,
        other_user: {
          id: row.other_user_id,
          email: row.other_user_email,
          full_name: row.other_user_name,
        },
        last_message: row.last_message_content ? {
          content: row.last_message_content,
          created_at: row.last_message_time,
        } : undefined,
      }));

      setConversations(chats);
      setError(null);
    } catch (err) {
      console.error('Chat\'ler yüklenirken hata:', err);
      setError('Chat\'ler yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser, supabase]);

  // Mesajları yükle
  const loadMessages = useCallback(async (chatId: string) => {
    if (!chatId) return;

    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select(`
          *,
          sender:profiles(id, email, full_name)
        `)
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Mesajlar yüklenirken hata:', error);
        setError('Mesajlar yüklenemedi');
        return;
      }

      setMessages(data || []);
      setError(null);
    } catch (err) {
      console.error('Mesajlar yüklenirken hata:', err);
      setError('Mesajlar yüklenemedi');
    }
  }, [supabase]);

  // Aktif chat'i seç
  const selectChat = useCallback((chat: Chat | null) => {
    setActiveConversation(chat);
    if (chat) {
      loadMessages(chat.id);
    } else {
      setMessages([]);
    }
  }, [loadMessages]);

  // Mesaj gönder
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || !activeConversation || !currentUser) return;

    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          chat_id: activeConversation.id,
          sender_id: currentUser.id,
          content: content.trim(),
        })
        .select(`
          *,
          sender:profiles(id, email, full_name)
        `)
        .single();

      if (error) {
        console.error('Mesaj gönderme hatası:', error);
        toast.error('Mesaj gönderilemedi');
        return;
      }


      
      // Optimistic update - mesajı hemen ekle
      setMessages(prev => [...prev, data]);
      
      // Chat'in updated_at'ini güncelle
      await supabase
        .from('chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', activeConversation.id);
    } catch (err) {
      console.error('Mesaj gönderme hatası:', err);
      toast.error('Mesaj gönderilemedi');
    }
  }, [activeConversation, currentUser, supabase]);

  // Chat oluştur - sadece birebir sohbetler
  const createConversation = useCallback(async (otherUserId: string) => {
    if (!currentUser || !otherUserId) return;

    try {
      // Yeni chat oluşturma fonksiyonunu kullan
      const { data: chatId, error } = await supabase.rpc('create_or_get_chat', {
        other_user_id: otherUserId
      });

      if (error) {
        console.error('Chat oluşturma hatası:', error);
        toast.error('Chat oluşturulamadı');
        return;
      }
      // Chat'leri yenile
      await loadConversations();
      return chatId;
    } catch (err) {
      console.error('Chat oluşturma hatası:', err);
      toast.error('Chat oluşturulamadı');
    }
  }, [currentUser, supabase, loadConversations]);

  // Realtime subscription
  useEffect(() => {
    if (!currentUser || !activeConversation) return;



    const channel = supabase
      .channel(`chat-messages-${activeConversation.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `chat_id=eq.${activeConversation.id}`,
        },
        async (payload) => {

          
          // Sender bilgisini al
          const { data: sender } = await supabase
            .from('profiles')
            .select('id, email, full_name')
            .eq('id', payload.new.sender_id)
            .single();

          const newMessage = {
            ...payload.new,
            sender: sender || undefined,
          } as ChatMessage;

          setMessages(prev => {
            // Duplicate kontrolü
            const exists = prev.some(msg => msg.id === newMessage.id);
            if (exists) return prev;
            
            return [...prev, newMessage];
          });
        }
      )
      .subscribe((_status) => {

      });

    subscriptionRef.current = channel;

    return () => {

      channel.unsubscribe();
    };
  }, [currentUser, activeConversation, supabase]);

  // İlk yükleme
  useEffect(() => {
    if (currentUser) {
      loadConversations();
    }
  }, [currentUser, loadConversations]);

  return {
    conversations,
    activeConversation,
    messages,
    isLoading,
    error,
    currentUser,
    setActiveConversation: selectChat,
    sendMessage,
    createConversation,
    loadConversations,
  };
}
