'use client';

import Image from 'next/image';
import Link from 'next/link';
import {
  AnimatedSection,
  StaggeredAnimation,
} from '@/components/ui/animated-section';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { StarDisplay } from '@/components/ui/star-rating';
import {
  Quote,
  Star,
  ArrowRight,
  Users,
  TrendingUp,
  Building2
} from 'lucide-react';

interface TestimonialItem {
  name: string;
  role: string;
  company: string;
  quote: string;
  rating: number;
  avatar?: string;
  results?: {
    metric: string;
    value: string;
    icon: any;
  };
}

export function PremiumTestimonials() {
  const testimonials: TestimonialItem[] = [
    {
      name: '<PERSON><PERSON> Yılmaz',
      role: '<PERSON>l Müdür',
      company: 'FitLife Spor Merkezi',
      quote:
        'Sportiva ile şube bazlı raporlamayla operasyonel kararlarımızı çok daha hızlı alıyoruz. Randevu çakışmaları neredeyse tamamen ortadan kalktı ve üye memnuniyetimiz %40 arttı.',
      rating: 5,
      avatar: '/placeholder.svg?height=48&width=48&query=user-avatar',
      results: {
        metric: 'Üye Artışı',
        value: '+40%',
        icon: Users
      }
    },
    {
      name: 'Seda Kaya',
      role: 'Operasyon Yöneticisi',
      company: 'PowerGym Zincirleri',
      quote:
        'Otomatik hatırlatma sistemi sayesinde no-show oranımız %60 azaldı. Müşteri iletişim süreçlerimiz tamamen dijitalleşti ve zaman tasarrufu muazzam.',
      rating: 5,
      avatar: '/placeholder.svg?height=48&width=48&query=user-avatar',
      results: {
        metric: 'No-Show Azalması',
        value: '-60%',
        icon: TrendingUp
      }
    },
    {
      name: 'Mehmet Öztürk',
      role: 'Kurucu & CEO',
      company: 'Elite Fitness Studio',
      quote:
        'Çoklu şube yönetimi özelliği ile 5 şubemizi tek panelden kontrol ediyoruz. Raporlar ve finansal görünürlük sayesinde yatırım kararlarımızı veri odaklı alıyoruz.',
      rating: 5,
      avatar: '/placeholder.svg?height=48&width=48&query=user-avatar',
      results: {
        metric: 'Şube Sayısı',
        value: '5 Lokasyon',
        icon: Building2
      }
    },
  ];

  const overallStats = [
    {
      value: '4.9/5',
      label: 'Ortalama Puan',
      description: '500+ değerlendirme'
    },
    {
      value: '%96',
      label: 'Tavsiye Oranı',
      description: 'Müşterilerimizin tavsiye etme oranı'
    },
    {
      value: '24sa',
      label: 'Destek Süresi',
      description: 'Ortalama sorun çözüm süresi'
    }
  ];

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-muted/30 via-background to-muted/20">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-grid-pattern opacity-10" />
      
      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <AnimatedSection animation="fade-up" delay={100}>
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4 px-4 py-2 border-primary/20 bg-primary/10">
              <Quote className="mr-2 h-4 w-4" />
              Müşteri Hikayeleri
            </Badge>
            
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                Başarı Hikayeleri
              </span>
              <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Gerçek Sonuçlar
              </span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Türkiye&apos;nin önde gelen spor salonları Sportiva ile nasıl büyüyor ve dijitalleşiyor.
            </p>
          </div>
        </AnimatedSection>

        {/* Stats Row */}
        <AnimatedSection animation="fade-up" delay={200}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16 max-w-4xl mx-auto">
            {overallStats.map((stat, _index) => (
              <div
                key={stat.label}
                className="text-center p-6 rounded-xl border border-border/50 bg-card/50 backdrop-blur"
              >
                <div className="text-3xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <h4 className="text-lg font-semibold text-foreground mb-1">
                  {stat.label}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {stat.description}
                </p>
              </div>
            ))}
          </div>
        </AnimatedSection>

        {/* Testimonials Grid */}
        <StaggeredAnimation
          animation="fade-up"
          staggerDelay={150}
          className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16"
        >
          {testimonials.map((testimonial, _index) => {
            const ResultIcon = testimonial.results?.icon;
            return (
              <div
                key={testimonial.name}
                className="group relative overflow-hidden rounded-2xl border border-border/50 bg-card/80 p-8 backdrop-blur transition-all duration-500 hover:border-primary/30 hover:-translate-y-2 hover:shadow-2xl"
              >
                {/* Background glow */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
                
                {/* Quote decoration */}
                <div className="absolute top-4 right-4 opacity-10">
                  <Quote className="h-12 w-12 text-primary" />
                </div>
                
                <div className="relative">
                  {/* Rating */}
                  <div className="mb-6">
                    <StarDisplay
                      rating={testimonial.rating}
                      totalReviews={undefined}
                      size="sm"
                    />
                  </div>

                  {/* Quote */}
                  <p className="text-foreground/90 mb-6 text-sm leading-relaxed italic">
                    &ldquo;{testimonial.quote}&rdquo;
                  </p>

                  {/* Results badge */}
                  {testimonial.results && ResultIcon && (
                    <div className="mb-6 inline-flex items-center gap-2 rounded-full bg-primary/10 px-3 py-1.5 text-sm">
                      <ResultIcon className="h-4 w-4 text-primary" />
                      <span className="font-semibold text-primary">
                        {testimonial.results.value}
                      </span>
                      <span className="text-muted-foreground">
                        {testimonial.results.metric}
                      </span>
                    </div>
                  )}

                  {/* Author info */}
                  <div className="flex items-center gap-4">
                    <div className="relative h-12 w-12 overflow-hidden rounded-full border-2 border-border">
                      <Image
                        src={testimonial.avatar || '/placeholder.svg?height=48&width=48&query=user-avatar'}
                        alt={`${testimonial.name} avatar`}
                        fill
                        sizes="48px"
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h4 className="font-semibold text-foreground">
                        {testimonial.name}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {testimonial.role}
                      </p>
                      <p className="text-xs text-primary font-medium">
                        {testimonial.company}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </StaggeredAnimation>

        {/* Bottom CTA */}
        <AnimatedSection animation="fade-up" delay={800}>
          <div className="text-center">
            <div className="inline-flex flex-col sm:flex-row items-center gap-4">
              <Button
                asChild
                size="lg"
                className="group h-12 px-8 text-base font-semibold shadow-lg transition-all duration-300 hover:shadow-xl"
              >
                <Link href="/onboarding">
                  Sen de başarı hikayene başla
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground mt-4">
              <Star className="inline h-4 w-4 text-yellow-500 mr-1" />
              500+ spor salonu Sportiva&apos;yı tercih ediyor
            </p>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}