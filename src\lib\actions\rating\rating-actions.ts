/**
 * Server actions for gym rating functionality
 * Following design document specifications for member validation and rating operations
 */

'use server';

import { createAction, createActionPublic } from '@/lib/actions/core/core';
import { ApiResponse } from '@/types/global/api';
import { 
  UserRating, 
  CreateRatingPayload, 
  UpdateRatingPayload, 
  DeleteRatingPayload
} from '@/types/features/rating';
import { 
  validateRatingForm, 
  validateMembershipForRating,
  createRatingRecord
} from '@/lib/utils/rating-utils';
import { getComprehensiveGymMembershipStatus } from '@/lib/actions/findGym/findgym-actions';

// ============================================================================
// RATING CRUD OPERATIONS
// ============================================================================

/**
 * Create a new gym rating
 * Validates membership and prevents duplicate ratings
 */
export async function createGymRating(
  payload: CreateRatingPayload
): Promise<ApiResponse<UserRating>> {
  return createAction<UserRating>(
    async (_, supabase, userId) => {
      const { gymId, rating, comment } = payload;

      // Validate form data
      const formValidation = validateRatingForm({ rating, comment });
      if (!formValidation.isValid) {
        throw new Error(formValidation.error);
      }

      // Check user membership status
      const membershipResponse = await getComprehensiveGymMembershipStatus(gymId);
      if (!membershipResponse.success || !membershipResponse.data) {
        throw new Error('Üyelik durumu kontrol edilirken bir hata oluştu');
      }

      const membershipValidation = validateMembershipForRating(
        membershipResponse.data.membershipStatus
      );
      if (!membershipValidation.isValid) {
        throw new Error(membershipValidation.error);
      }

      // Check for existing rating
      const { data: existingRating } = await supabase
        .from('gym_reviews')
        .select('id')
        .eq('gym_id', gymId)
        .eq('profile_id', userId)
        .maybeSingle();

      if (existingRating) {
        throw new Error('Bu salon için zaten bir değerlendirmeniz bulunmaktadır');
      }

      // Validate userId
      if (!userId) {
        throw new Error('Giriş yapmanız gerekmektedir');
      }

      // Create rating record
      const ratingRecord = createRatingRecord(userId, gymId, { rating, comment });
      const now = new Date().toISOString();

      const { data: newRating, error: insertError } = await supabase
        .from('gym_reviews')
        .insert({
          ...ratingRecord,
          created_at: now,
          updated_at: now,
        })
        .select()
        .single();

      if (insertError) {
        console.error('Rating insert error:', insertError);
        throw new Error('Değerlendirme kaydedilirken bir hata oluştu');
      }

      // Update gym statistics
      await updateGymRatingStatistics(gymId);

      return newRating as UserRating;
    },
    {
      revalidatePaths: [`/gym/*`],
    }
  );
}

/**
 * Update existing gym rating
 * Verifies ownership before updating
 */
export async function updateGymRating(
  payload: UpdateRatingPayload
): Promise<ApiResponse<UserRating>> {
  return createAction<UserRating>(
    async (_, supabase, userId) => {
      const { ratingId, rating, comment } = payload;

      // Validate form data
      const formValidation = validateRatingForm({ rating, comment });
      if (!formValidation.isValid) {
        throw new Error(formValidation.error);
      }

      // Verify rating ownership
      const { data: existingRating, error: checkError } = await supabase
        .from('gym_reviews')
        .select('profile_id, gym_id')
        .eq('id', ratingId)
        .single();

      if (checkError || !existingRating) {
        throw new Error('Değerlendirme bulunamadı');
      }

      if (existingRating.profile_id !== userId) {
        throw new Error('Bu değerlendirmeyi güncelleme yetkiniz yok');
      }

      // Update rating
      const { data: updatedRating, error: updateError } = await supabase
        .from('gym_reviews')
        .update({
          rating,
          comment: comment?.trim() || null,
          updated_at: new Date().toISOString(),
        })
        .eq('id', ratingId)
        .select()
        .single();

      if (updateError) {
        console.error('Rating update error:', updateError);
        throw new Error('Değerlendirme güncellenirken bir hata oluştu');
      }

      // Update gym statistics
      await updateGymRatingStatistics(existingRating.gym_id);

      return updatedRating as UserRating;
    },
    {
      revalidatePaths: [`/gym/*`],
    }
  );
}

/**
 * Delete gym rating
 * Verifies ownership before deletion
 */
export async function deleteGymRating(
  payload: DeleteRatingPayload
): Promise<ApiResponse<{ success: boolean }>> {
  return createAction<{ success: boolean }>(
    async (_, supabase, userId) => {
      const { ratingId } = payload;

      // Verify rating ownership and get gym_id for stats update
      const { data: existingRating, error: checkError } = await supabase
        .from('gym_reviews')
        .select('profile_id, gym_id')
        .eq('id', ratingId)
        .single();

      if (checkError || !existingRating) {
        throw new Error('Değerlendirme bulunamadı');
      }

      if (existingRating.profile_id !== userId) {
        throw new Error('Bu değerlendirmeyi silme yetkiniz yok');
      }

      // Delete rating
      const { error: deleteError } = await supabase
        .from('gym_reviews')
        .delete()
        .eq('id', ratingId);

      if (deleteError) {
        console.error('Rating delete error:', deleteError);
        throw new Error('Değerlendirme silinirken bir hata oluştu');
      }

      // Update gym statistics
      await updateGymRatingStatistics(existingRating.gym_id);

      return { success: true };
    },
    {
      revalidatePaths: [`/gym/*`],
    }
  );
}

/**
 * Get user's rating for a specific gym
 */
export async function getUserGymRating(
  gymId: string
): Promise<ApiResponse<UserRating | null>> {
  return createAction<UserRating | null>(
    async (_, supabase, userId) => {
      const { data: rating, error } = await supabase
        .from('gym_reviews')
        .select('*')
        .eq('gym_id', gymId)
        .eq('profile_id', userId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error('User rating fetch error:', error);
        throw new Error('Değerlendirme getirilirken bir hata oluştu');
      }

      return rating as UserRating | null;
    }
  );
}

/**
 * Get user's rating for a specific gym (public version for server components)
 */
export async function getUserGymRatingPublic(
  gymId: string,
  userId: string
): Promise<ApiResponse<UserRating | null>> {
  return createActionPublic<UserRating | null>(
    async (_, supabase) => {
      const { data: rating, error } = await supabase
        .from('gym_reviews')
        .select('*')
        .eq('gym_id', gymId)
        .eq('profile_id', userId)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error('User rating fetch error:', error);
        throw new Error('Değerlendirme getirilirken bir hata oluştu');
      }

      return rating as UserRating | null;
    }
  );
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Update gym rating statistics
 * Recalculates average rating and count
 */
async function updateGymRatingStatistics(gymId: string): Promise<void> {
  try {
    const { createAnonClient } = await import('@/lib/supabase/server');
    const supabase = await createAnonClient();

    // Get all ratings for the gym
    const { data: ratings, error: ratingsError } = await supabase
      .from('gym_reviews')
      .select('rating')
      .eq('gym_id', gymId);

    if (ratingsError) {
      console.error('Error fetching ratings for stats update:', ratingsError);
      return;
    }

    // Calculate statistics
    const validRatings = ratings?.filter(r => r.rating && r.rating >= 1 && r.rating <= 5) || [];
    const averageRating = validRatings.length > 0 
      ? validRatings.reduce((sum, r) => sum + r.rating, 0) / validRatings.length 
      : 0;
    const reviewCount = validRatings.length;

    // Update gym statistics
    const { error: updateError } = await supabase
      .from('gyms')
      .update({
        average_rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        review_count: reviewCount,
        updated_at: new Date().toISOString(),
      })
      .eq('id', gymId);

    if (updateError) {
      console.error('Error updating gym statistics:', updateError);
    }
  } catch (error) {
    console.error('Error in updateGymRatingStatistics:', error);
  }
}