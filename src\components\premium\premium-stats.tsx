'use client';

import {
  AnimatedSection,
  AnimatedCounter,
} from '@/components/ui/animated-section';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Users, 
  Clock, 
  Star, 
  Building2, 
  Calendar,
  Award,
  Zap
} from 'lucide-react';

export function PremiumStats() {
  const mainStats = [
    {
      label: 'Aktif <PERSON>ye',
      value: 25000,
      suffix: '+',
      icon: Users,
      color: 'from-blue-500/20 via-blue-500/10 to-blue-500/5',
      iconColor: 'text-blue-600',
      borderColor: 'border-blue-200/30',
      description: 'Platform üzerinde kayıtlı üye'
    },
    {
      label: 'Spor Salonu',
      value: 180,
      suffix: '+',
      icon: Building2,
      color: 'from-green-500/20 via-green-500/10 to-green-500/5',
      iconColor: 'text-green-600',
      borderColor: 'border-green-200/30',
      description: 'Türkiye genelinde partner salon'
    },
    {
      label: '<PERSON><PERSON><PERSON><PERSON>',
      value: 120000,
      suffix: '+',
      icon: Calendar,
      color: 'from-orange-500/20 via-orange-500/10 to-orange-500/5',
      iconColor: 'text-orange-600',
      borderColor: 'border-orange-200/30',
      description: 'Başarıyla tamamlanan rezervasyon'
    },
    {
      label: 'Müşteri Memnuniyeti',
      value: 98,
      suffix: '%',
      icon: Star,
      color: 'from-purple-500/20 via-purple-500/10 to-purple-500/5',
      iconColor: 'text-purple-600',
      borderColor: 'border-purple-200/30',
      description: 'Kullanıcı geri bildirimlerine göre'
    },
  ];

  const secondaryStats = [
    {
      icon: Clock,
      value: '99.95',
      suffix: '%',
      label: 'Uptime Garantisi',
      description: 'Kesintisiz hizmet süresi'
    },
    {
      icon: Zap,
      value: '< 2',
      suffix: 'sn',
      label: 'Yanıt Süresi',
      description: 'Ortalama API yanıt süresi'
    },
    {
      icon: Award,
      value: 'ISO',
      suffix: ' 27001',
      label: 'Güvenlik Sertifikası',
      description: 'Uluslararası güvenlik standardı'
    }
  ];

  return (
    <section className="py-20 lg:py-28 bg-gradient-to-br from-muted/30 via-background to-muted/20">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-20" />
      
      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <AnimatedSection animation="fade-up" delay={100}>
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4 px-4 py-2 border-primary/20 bg-primary/10">
              <TrendingUp className="mr-2 h-4 w-4" />
              Platform İstatistikleri
            </Badge>
            
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                Güvenilir
              </span>
              <span className="block bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Büyüyen Platform
              </span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Her gün binlerce kullanıcının güvendiği platform ile siz de büyümeye başlayın.
            </p>
          </div>
        </AnimatedSection>

        {/* Main Stats Grid */}
        <div className="grid grid-cols-2 gap-6 md:grid-cols-4 mb-16">
          {mainStats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <AnimatedSection
                key={stat.label}
                animation="fade-up"
                delay={200 + (index * 100)}
              >
                <div className={`group relative overflow-hidden rounded-2xl border ${stat.borderColor} bg-gradient-to-br ${stat.color} p-6 md:p-8 text-center backdrop-blur transition-all duration-500 hover:border-primary/40 hover:-translate-y-2 hover:shadow-xl`}>
                  {/* Background glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
                  
                  {/* Floating decoration */}
                  <div className="absolute top-2 right-2 h-8 w-8 rounded-full bg-gradient-to-br from-background/20 to-transparent blur-sm" />

                  <div className="relative">
                    <div className="mb-4 flex justify-center">
                      <div className={`bg-background/90 backdrop-blur rounded-xl p-3 shadow-lg transition-transform duration-300 group-hover:scale-110 ${stat.iconColor}`}>
                        <IconComponent className="h-6 w-6 md:h-8 md:w-8" />
                      </div>
                    </div>

                    <div className="mb-3">
                      <div className="text-3xl md:text-4xl font-extrabold text-foreground">
                        <AnimatedCounter
                          target={stat.value}
                          suffix={stat.suffix}
                          className="animate-counter"
                        />
                      </div>
                    </div>

                    <h3 className="text-sm md:text-base font-bold text-foreground mb-2">
                      {stat.label}
                    </h3>
                    
                    <p className="text-xs text-muted-foreground leading-relaxed">
                      {stat.description}
                    </p>
                  </div>
                </div>
              </AnimatedSection>
            );
          })}
        </div>

        {/* Secondary Stats */}
        <AnimatedSection animation="fade-up" delay={800}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {secondaryStats.map((stat, _index) => {
              const IconComponent = stat.icon;
              return (
                <div
                  key={stat.label}
                  className="group flex items-center gap-4 rounded-xl border border-border/50 bg-card/80 p-6 backdrop-blur transition-all duration-300 hover:border-primary/30 hover:shadow-lg"
                >
                  <div className="flex-shrink-0">
                    <div className="bg-primary/10 rounded-lg p-3 transition-colors group-hover:bg-primary/20">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-baseline gap-1 mb-1">
                      <span className="text-2xl font-bold text-foreground">
                        {stat.value}
                      </span>
                      <span className="text-lg font-semibold text-primary">
                        {stat.suffix}
                      </span>
                    </div>
                    
                    <h4 className="text-sm font-semibold text-foreground mb-1">
                      {stat.label}
                    </h4>
                    
                    <p className="text-xs text-muted-foreground">
                      {stat.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </AnimatedSection>

        {/* Bottom section with additional context */}
        <AnimatedSection animation="fade-up" delay={1000}>
          <div className="text-center mt-16">
            <div className="inline-flex items-center gap-2 rounded-full border border-green-200/50 bg-green-500/10 px-4 py-2 text-sm font-medium text-green-700">
              <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
              <span>Anlık veriler • Son güncelleme: Az önce</span>
            </div>
          </div>
        </AnimatedSection>
      </div>
    </section>
  );
}
