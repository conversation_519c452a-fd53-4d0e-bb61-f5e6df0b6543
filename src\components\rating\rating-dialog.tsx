'use client';

import { useState, useTransition } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { StarRating } from '@/components/ui/star-rating';
import { Loader2, Star, MessageCircle } from 'lucide-react';
import { toast } from 'sonner';
import { 
  RatingDialogProps, 
  RatingFormData,
  RatingSubmissionState
} from '@/types/features/rating';
import { 
  validateRatingForm, 
  getRatingSuccessMessage
} from '@/lib/utils/rating-utils';
import { RATING_CONSTRAINTS } from '@/types/features/rating';
import { createGymRating, updateGymRating } from '@/lib/actions/rating/rating-actions';

export function RatingDialog({
  isOpen,
  onOpenChange,
  gymId,
  onRatingSubmit,
  existingRating,
}: RatingDialogProps) {
  const [rating, setRating] = useState(existingRating?.rating || 0);
  const [comment, setComment] = useState(existingRating?.comment || '');
  const [submissionState, setSubmissionState] = useState<RatingSubmissionState>('idle');
  const [isPending, startTransition] = useTransition();

  const isEditing = !!existingRating;
  const isSubmitting = submissionState === 'submitting' || isPending;

  const handleSubmit = async () => {
    // Validate form data
    const formData: RatingFormData = { rating, comment: comment.trim() };
    const validation = validateRatingForm(formData);
    
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    setSubmissionState('submitting');

    startTransition(async () => {
      try {
        let response;
        
        if (isEditing && existingRating) {
          // Update existing rating
          response = await updateGymRating({
            ratingId: existingRating.id,
            rating,
            comment: comment.trim() || undefined,
          });
        } else {
          // Create new rating
          response = await createGymRating({
            gymId,
            rating,
            comment: comment.trim() || undefined,
          });
        }

        if (response.success && response.data) {
          const successMessage = getRatingSuccessMessage(isEditing ? 'update' : 'create');
          toast.success(successMessage);
          setSubmissionState('success');
          onRatingSubmit(response.data);
          onOpenChange(false);
          
          // Reset form if creating new rating
          if (!isEditing) {
            setRating(0);
            setComment('');
          }
        } else {
          throw new Error(response.error || 'Beklenmeyen bir hata oluştu');
        }
      } catch (error) {
        console.error('Rating submission error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Değerlendirme kaydedilirken bir hata oluştu';
        toast.error(errorMessage);
        setSubmissionState('error');
      }
    });
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onOpenChange(false);
      setSubmissionState('idle');
    }
  };

  const canSubmit = rating >= RATING_CONSTRAINTS.MIN_RATING && 
                   rating <= RATING_CONSTRAINTS.MAX_RATING && 
                   !isSubmitting;

  const characterCount = comment.length;
  const isCommentTooLong = characterCount > RATING_CONSTRAINTS.MAX_COMMENT_LENGTH;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <Star className="h-6 w-6 text-primary" />
          </div>
          <DialogTitle className="text-xl font-bold">
            {isEditing ? 'Değerlendirmenizi Güncelleyin' : 'Salonu Değerlendirin'}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {isEditing 
              ? 'Değerlendirmenizi ve yorumunuzu güncelleyebilirsiniz.'
              : 'Deneyiminizi paylaşın ve diğer üyelere yardımcı olun.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Rating Section */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Puanınız <span className="text-destructive">*</span>
            </Label>
            <div className="flex justify-center">
              <StarRating
                rating={rating}
                onRatingChange={setRating}
                size="lg"
                showLabel
                className="py-2"
              />
            </div>
          </div>

          {/* Comment Section */}
          <div className="space-y-3">
            <Label htmlFor="rating-comment" className="text-sm font-medium flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              Yorumunuz (İsteğe bağlı)
            </Label>
            <div className="space-y-2">
              <Textarea
                id="rating-comment"
                placeholder="Salonla ilgili deneyiminizi paylaşın..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={4}
                className={`resize-none ${isCommentTooLong ? 'border-destructive' : ''}`}
                disabled={isSubmitting}
                maxLength={RATING_CONSTRAINTS.MAX_COMMENT_LENGTH + 50} // Allow typing beyond limit for better UX
              />
              <div className="flex justify-between text-xs">
                <span className={`${isCommentTooLong ? 'text-destructive' : 'text-muted-foreground'}`}>
                  {characterCount}/{RATING_CONSTRAINTS.MAX_COMMENT_LENGTH} karakter
                </span>
                {isCommentTooLong && (
                  <span className="text-destructive">
                    Yorum çok uzun
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="flex-1"
            >
              İptal
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit || isCommentTooLong}
              className="flex-1"
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Güncelle' : 'Değerlendir'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}