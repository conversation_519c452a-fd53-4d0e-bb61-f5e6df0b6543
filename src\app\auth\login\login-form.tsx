'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EnhancedAuthError } from '@/components/auth/enhanced-auth-error';
import { SubmitButton } from '@/components/auth/submit-button';
import { toast } from 'sonner';
import { ApiResponse } from '@/types/global/api';
import Link from 'next/link';

interface LoginFormProps {
  error?: string;
  identifierError?: string;
  passwordError?: string;
  context?: string;
  onEmailLogin: (
    formData: FormData
  ) => Promise<
    ApiResponse<{ success: boolean; error?: string | null; message: string }>
  >;
  onPhonePasswordLogin: (
    formData: FormData
  ) => Promise<
    ApiResponse<{ success: boolean; error?: string | null; message: string }>
  >;
  onSendPhoneOtp: (
    formData: FormData
  ) => Promise<ApiResponse<{ message: string }>>;
  onVerifyPhoneOtp: (
    formData: FormData
  ) => Promise<ApiResponse<{ userId: string; isNewUser: boolean }>>;
}

/**
 * Detect if input is email or phone number
 */
function detectInputType(value: string): 'email' | 'phone' {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const phonePattern = /^(\+90|0)?[5][0-9]{9}$/;
  const cleanValue = value.replace(/[\s\-\(\)]/g, '');

  if (emailPattern.test(value)) {
    return 'email';
  } else if (phonePattern.test(cleanValue)) {
    return 'phone';
  }

  return 'email';
}

export function LoginForm({
  error,
  identifierError,
  passwordError,
  context,
  onEmailLogin,
  onPhonePasswordLogin,
  onSendPhoneOtp,
  onVerifyPhoneOtp,
}: LoginFormProps) {
  const router = useRouter();
  const [clientError, setClientError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPhoneVerification, setShowPhoneVerification] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerified, setIsVerified] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [isVerifyingCode, setIsVerifyingCode] = useState(false);
  const [canResend, setCanResend] = useState(true);
  const [timeUntilResend, setTimeUntilResend] = useState(0);

  const handleInitialSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const form = e.currentTarget;
    const formDataObj = new FormData(form);
    const identifier = formDataObj.get('identifier') as string;
    const password = formDataObj.get('password') as string;

    // Validation
    if (!identifier) {
      setClientError('Telefon numarası veya e-posta gereklidir');
      return;
    }

    if (!password) {
      setClientError('Şifre gereklidir');
      return;
    }

    setClientError('');

    // Detect input type
    const inputType = detectInputType(identifier);

    if (inputType === 'phone') {
      // Telefon numarası için iki seçenek sun
      setPhoneNumber(identifier);

      if (password) {
        // Şifre varsa şifre ile giriş dene
        setIsSubmitting(true);
        try {
          const phoneFormData = new FormData();
          phoneFormData.append('phone', identifier);
          phoneFormData.append('password', password);

          const result = await onPhonePasswordLogin(phoneFormData);

          if (result.success) {
            router.push('/dashboard');
          } else {
            // Şifre ile giriş başarısızsa
            setClientError('Şifre hatalı. ');
          }
        } catch (error) {
          setClientError('Şifre ile giriş başarısız. ');
        } finally {
          setIsSubmitting(false);
        }
      }
    } else {
      // Email ile giriş
      setIsSubmitting(true);
      try {
        const emailFormData = new FormData();
        emailFormData.append('email', identifier);
        emailFormData.append('password', password);

        const result = await onEmailLogin(emailFormData);

        if (result.success) {
          router.push('/dashboard');
        } else {
          setClientError(result.error || 'E-posta giriş işlemi başarısız');
        }
      } catch (error) {
        setClientError('E-posta giriş işlemi başarısız');
        console.error('Email login error:', error);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const sendOtpToPhone = async (phone: string) => {
    setIsSendingCode(true);
    setClientError('');

    try {
      const phoneFormData = new FormData();
      phoneFormData.append('phone', phone);

      const result = await onSendPhoneOtp(phoneFormData);

      if (result.success) {
        setShowPhoneVerification(true);
        toast.success('Doğrulama kodu gönderildi!');
      } else {
        setClientError(result.error || 'Doğrulama kodu gönderilemedi');
      }
    } catch (error) {
      setClientError('Telefon doğrulama başlatılamadı');
      console.error('Phone verification error:', error);
    } finally {
      setIsSendingCode(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode || !phoneNumber) {
      setClientError('Doğrulama kodu gereklidir');
      return;
    }

    setIsVerifyingCode(true);
    setClientError('');

    try {
      const verifyFormData = new FormData();
      verifyFormData.append('phone', phoneNumber);
      verifyFormData.append('token', verificationCode);

      const result = await onVerifyPhoneOtp(verifyFormData);

      if (result.success && result.data) {
        setIsVerified(true);
        toast.success('Giriş başarılı!');

        // Yeni kullanıcı ise onboarding'e, mevcut kullanıcı ise panel'e yönlendir
        if (result.data.isNewUser) {
          router.push('/onboarding');
        } else {
          router.push('/dashboard');
        }
      } else {
        setClientError(result.error || 'Doğrulama kodu geçersiz');
      }
    } catch (error) {
      setClientError('Doğrulama kodu geçersiz');
      console.error('Verification error:', error);
    } finally {
      setIsVerifyingCode(false);
    }
  };

  const handleResendCode = async () => {
    if (!phoneNumber) return;
    await sendOtpToPhone(phoneNumber);

    setCanResend(false);
    setTimeUntilResend(60);

    // Start countdown
    const interval = setInterval(() => {
      setTimeUntilResend(prev => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  if (showPhoneVerification && !isVerified) {
    return (
      <div className="space-y-4">
        {/* Error Messages */}
        {clientError && (
          <EnhancedAuthError
            error={clientError}
            context={{
              action: 'login',
              method: 'phone',
              stage: 'verification',
            }}
            onRetry={() => setClientError('')}
          />
        )}

        <div className="text-center">
          <h3 className="text-lg font-semibold">Telefon Doğrulama</h3>
          <p className="text-muted-foreground mt-1 text-sm">
            {phoneNumber} numarasına gönderilen doğrulama kodunu giriniz
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="verificationCode">Doğrulama Kodu</Label>
            <Input
              id="verificationCode"
              type="text"
              placeholder="123456"
              value={verificationCode}
              onChange={e => setVerificationCode(e.target.value)}
              maxLength={6}
              disabled={isVerifyingCode}
            />
          </div>

          <Button
            onClick={handleVerifyCode}
            disabled={isVerifyingCode || !verificationCode}
            className="w-full"
          >
            {isVerifyingCode ? 'Doğrulanıyor...' : 'Giriş Yap'}
          </Button>

          <div className="text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResendCode}
              disabled={!canResend || isSendingCode}
            >
              {isSendingCode
                ? 'Gönderiliyor...'
                : canResend
                  ? 'Kodu Tekrar Gönder'
                  : `${timeUntilResend} saniye sonra tekrar gönderebilirsiniz`}
            </Button>
          </div>

          <div className="text-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowPhoneVerification(false);
                setVerificationCode('');
                setClientError('');
              }}
            >
              Geri Dön
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Error Messages */}
      {(error || clientError || identifierError || passwordError) && (
        <EnhancedAuthError
          error={error || clientError || identifierError || passwordError || ''}
          context={{
            action: 'login',
            method: context?.includes('email')
              ? 'email'
              : context?.includes('phone')
                ? 'phone'
                : undefined,
            stage: context?.includes('verification')
              ? 'verification'
              : 'initial',
          }}
          onRetry={() => setClientError('')}
        />
      )}

      <form onSubmit={handleInitialSubmit} className="space-y-4">
        <div>
          <Label htmlFor="identifier">Telefon Numarası veya E-posta</Label>
          <Input
            id="identifier"
            name="identifier"
            type="text"
            placeholder="5551234567 veya <EMAIL>"
            required
            disabled={isSubmitting}
          />
        </div>

        <div>
          <div className="mb-1 flex items-center justify-between">
            <Label htmlFor="password">Şifre</Label>
            <Link
              href="/auth/forgot-password"
              className="text-primary hover:text-primary/80 text-sm hover:underline"
            >
              Şifremi unuttum?
            </Link>
          </div>
          <Input
            id="password"
            name="password"
            type="password"
            placeholder="Şifrenizi girin"
            required
            disabled={isSubmitting}
          />
        </div>

        <SubmitButton disabled={isSubmitting}>
          {isSubmitting
            ? 'Giriş yapılıyor...'
            : isSendingCode
              ? 'Kod gönderiliyor...'
              : 'Giriş Yap'}
        </SubmitButton>
      </form>
    </div>
  );
}
