'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON>,
  Users,
  CreditCard,
  BarChart4,
  Calendar,
  ArrowRight,
  Star,
  Zap,
  Play,
  Crown,
  UserCheck,
  Building,
  ShieldCheck,
  Award,
  TrendingUp,
} from 'lucide-react';
import Link from 'next/link';
import { useState, useEffect } from 'react';

export function FeaturesHero() {
  const [currentHighlight, setCurrentHighlight] = useState(0);
  const [stats, setStats] = useState({
    gyms: 0,
    members: 0,
    transactions: 0
  });

  const highlights = [
    { 
      icon: Users, 
      text: '<PERSON>ye Yönetimi',
      description: 'Binlerce üyeyi kolayca yönetin',
      stat: '50K+'
    },
    { 
      icon: CreditCard, 
      text: 'Ödeme Sistemi',
      description: '<PERSON>ü<PERSON>li ve hızlı ödeme çözümleri',
      stat: '₺2M+'
    },
    { 
      icon: BarChart4, 
      text: '<PERSON>litik Raporlar',
      description: '<PERSON><PERSON><PERSON><PERSON> iş zekası ve analizler',
      stat: '100+'
    },
    { 
      icon: Calendar, 
      text: '<PERSON><PERSON><PERSON>',
      description: 'Otomatik randevu ve program yönetimi',
      stat: '1000+'
    },
  ];

  const userTypes = [
    {
      type: 'Salon Sahibi',
      icon: Building,
      description: 'İşletmenizi büyütün',
      color: 'from-blue-500 to-blue-600'
    },
    {
      type: 'Yönetici',
      icon: UserCheck,
      description: 'Operasyonları optimize edin',
      color: 'from-purple-500 to-purple-600'
    },
    {
      type: 'Antrenör',
      icon: Dumbbell,
      description: 'Müşterilerinizi takip edin',
      color: 'from-green-500 to-green-600'
    }
  ];

  const trustIndicators = [
    { icon: ShieldCheck, text: 'ISO 27001 Sertifikalı', description: 'Güvenli veri koruması' },
    { icon: Award, text: '99.9% Uptime', description: 'Kesintisiz hizmet garantisi' },
    { icon: TrendingUp, text: '%40 Büyüme', description: 'Ortalama işletme büyümesi' },
  ];

  // Animated counter for stats
  useEffect(() => {
    const animateStats = () => {
      const targets = { gyms: 250, members: 50000, transactions: 2000000 };
      const duration = 2000;
      const steps = 60;
      const stepTime = duration / steps;
      
      let step = 0;
      const timer = setInterval(() => {
        step++;
        const progress = step / steps;
        const easeOut = 1 - Math.pow(1 - progress, 3);
        
        setStats({
          gyms: Math.floor(targets.gyms * easeOut),
          members: Math.floor(targets.members * easeOut),
          transactions: Math.floor(targets.transactions * easeOut)
        });
        
        if (step >= steps) {
          clearInterval(timer);
          setStats(targets);
        }
      }, stepTime);
    };

    animateStats();
  }, []);

  // Auto-rotate highlights
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentHighlight((prev) => (prev + 1) % highlights.length);
    }, 3000);

    return () => clearInterval(timer);
  }, [highlights.length]);

  return (
    <section className="from-background via-muted/30 to-background relative overflow-hidden bg-gradient-to-br py-20 lg:py-32" role="banner" aria-label="Sportiva ana hero bölümü">
      {/* Enhanced Background Elements */}
      <div className="bg-grid-pattern absolute inset-0 opacity-5" aria-hidden="true"></div>
      
      {/* Animated Gradient Orbs */}
      <div className="bg-primary/10 absolute top-20 left-10 h-72 w-72 rounded-full blur-3xl animate-pulse" aria-hidden="true"></div>
      <div className="bg-primary/5 absolute right-10 bottom-20 h-96 w-96 rounded-full blur-3xl animate-pulse delay-1000" aria-hidden="true"></div>
      <div className="bg-secondary/10 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-64 w-64 rounded-full blur-2xl animate-pulse delay-500" aria-hidden="true"></div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="mx-auto max-w-6xl">
          {/* Enhanced Badge */}
          <div className="text-center mb-8">
            <Badge
              variant="outline"
              className="mb-6 px-6 py-3 text-sm font-medium bg-background/50 backdrop-blur-sm border-primary/20"
              role="status"
              aria-label="Türkiye'nin #1 Spor Salonu Yazılımı"
            >
              <Zap className="mr-2 h-4 w-4 animate-pulse" />
              Türkiye&apos;nin #1 Spor Salonu Yazılımı
            </Badge>
            
            {/* Live Stats */}
            <div className="flex flex-wrap justify-center gap-6 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{stats.gyms}+</div>
                <div className="text-sm text-muted-foreground">Aktif Salon</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{(stats.members / 1000).toFixed(0)}K+</div>
                <div className="text-sm text-muted-foreground">Mutlu Üye</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">₺{(stats.transactions / 1000000).toFixed(1)}M+</div>
                <div className="text-sm text-muted-foreground">İşlem Hacmi</div>
              </div>
            </div>
          </div>

          {/* Enhanced Main Heading with Animation */}
          <div className="text-center mb-8">
            <h1 className="text-primary mb-6 text-4xl leading-tight font-bold md:text-5xl lg:text-7xl">
              Spor Salonu Yönetiminde
              <span className=" block bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Dijital Devrim
              </span>
            </h1>

            {/* Dynamic Description with Current Feature Highlight */}
            <p className="text-muted-foreground mx-auto mb-8 max-w-4xl text-lg leading-relaxed md:text-xl">
              Sportiva ile spor salonu işletmeciliğinin her alanında dijital dönüşümü yaşayın. 
              <span className="text-primary font-semibold">
                {highlights[currentHighlight].description}
              </span>
              {' '}ve daha fazlası için modern araçlar, akıllı analizler kullanın.
            </p>
          </div>

          {/* Interactive Feature Showcase */}
          <div className="mb-12" role="region" aria-label="Özellik vitrini">
            {/* Animated Feature Highlights */}
            <div className="mb-8 flex flex-wrap items-center justify-center gap-4" role="tablist" aria-label="Özellik kategorileri">
              {highlights.map((highlight, index) => {
                const IconComponent = highlight.icon;
                const isActive = index === currentHighlight;
                return (
                  <button
                    key={index}
                    role="tab"
                    aria-selected={isActive}
                    aria-controls={`feature-panel-${index}`}
                    tabIndex={isActive ? 0 : -1}
                    className={`bg-muted/50 border-border/50 flex items-center gap-3 rounded-full border px-6 py-3 transition-all duration-500 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary
                      ${isActive ? 'bg-primary/10 border-primary/30 scale-110 shadow-lg' : 'hover:bg-muted/70'}
                    `}
                    onClick={() => setCurrentHighlight(index)}
                    onKeyDown={(e) => {
                      if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                        e.preventDefault();
                        const nextIndex = e.key === 'ArrowLeft' 
                          ? (index - 1 + highlights.length) % highlights.length
                          : (index + 1) % highlights.length;
                        setCurrentHighlight(nextIndex);
                      }
                    }}
                  >
                    <IconComponent className={`h-5 w-5 transition-colors ${isActive ? 'text-primary animate-pulse' : 'text-muted-foreground'}`} aria-hidden="true" />
                    <div className="text-left">
                      <div className={`text-sm font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>
                        {highlight.text}
                      </div>
                      {isActive && (
                        <div className="text-xs text-muted-foreground" aria-live="polite">
                          {highlight.stat}
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
            
            {/* Demo Button */}
            <div className="text-center mb-8">
              <Button variant="outline" size="lg" className="bg-background/50 backdrop-blur-sm">
                <Play className="mr-2 h-5 w-5" />
                Canlı Demo İzleyin
              </Button>
            </div>
          </div>

          {/* Role-Based CTA Section */}
          <div className="mb-12">
            <h3 className="text-center text-lg font-semibold mb-6 text-muted-foreground">
              Rolünüzü seçin ve hemen başlayın
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              {userTypes.map((userType, index) => {
                const IconComponent = userType.icon;
                return (
                  <Link key={index} href="/auth/register">
                    <div className={`p-6 rounded-2xl bg-gradient-to-br ${userType.color} bg-opacity-10 border border-white/10 hover:scale-105 transition-all duration-300 cursor-pointer group`}>
                      <div className="text-center">
                        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br ${userType.color} mb-4`}>
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <h4 className="font-semibold text-foreground mb-2">{userType.type}</h4>
                        <p className="text-sm text-muted-foreground">{userType.description}</p>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
            
            {/* Primary CTA Buttons */}
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Button asChild size="lg" className="shadow-xl bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                <Link href="/auth/register">
                  <Crown className="mr-2 h-5 w-5" />
                  Ücretsiz Başlayın - 30 Gün
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild className="bg-background/50 backdrop-blur-sm">
                <Link href="/features">
                  Tüm Özellikleri Keşfedin
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Enhanced Trust Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            {trustIndicators.map((indicator, index) => {
              const IconComponent = indicator.icon;
              return (
                <div key={index} className="text-center p-6 rounded-xl bg-muted/30 backdrop-blur-sm border border-border/50">
                  <IconComponent className="text-primary h-8 w-8 mx-auto mb-3" />
                  <div className="font-semibold text-foreground mb-1">{indicator.text}</div>
                  <div className="text-sm text-muted-foreground">{indicator.description}</div>
                </div>
              );
            })}
          </div>
          
          {/* Additional Trust Elements */}
          <div className="text-center mt-8">
            <div className="text-muted-foreground text-sm mb-4">
              Kredi kartı gerekmez • Anında kurulum • İstediğiniz zaman iptal
            </div>
            <div className="flex flex-wrap items-center justify-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500" />
                <span>4.9/5 Müşteri Memnuniyeti</span>
              </span>
              <span>•</span>
              <span>500+ Başarılı Salon</span>
              <span>•</span>
              <span>7/24 Türkçe Destek</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
