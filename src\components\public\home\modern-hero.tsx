'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AnimatedSection } from '@/components/ui/animated-section';
import {
  ArrowRight,
  Play,
  Star,
  Users,
  TrendingUp,
  Shield,
  CheckCircle,
  Sparkles
} from 'lucide-react';

export function ModernHero() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-primary/5 to-background pt-16 pb-24 lg:pt-20 lg:pb-32">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-30" />
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
      
      {/* Floating elements */}
      <div className="absolute top-20 left-10 h-32 w-32 rounded-full bg-primary/10 blur-3xl" />
      <div className="absolute bottom-20 right-10 h-24 w-24 rounded-full bg-secondary/10 blur-2xl" />
      
      <div className="container relative mx-auto px-4">
        <div className="mx-auto max-w-7xl">
          <div className="grid items-center gap-16 lg:grid-cols-2">
            {/* Left Content */}
            <div className="text-center lg:text-left">
              <AnimatedSection animation="fade-up" delay={100}>
                <Badge 
                  variant="outline" 
                  className="mb-6 bg-primary/10 border-primary/20 text-primary px-4 py-2"
                >
                  <Sparkles className="mr-2 h-4 w-4" />
                  Türkiye&apos;nin #1 Dijital Platform
                </Badge>
                
                <h1 className="text-5xl leading-tight font-extrabold tracking-tight text-balance md:text-6xl lg:text-7xl">
                  <span className="bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                    Spor Salonunuzu
                  </span>
                  <span className="block bg-gradient-to-r from-primary via-primary to-primary/70 bg-clip-text text-transparent">
                    Dijital Dönüştür
                  </span>
                </h1>
                
                <p className="mx-auto mt-6 max-w-[65ch] text-xl text-muted-foreground text-pretty md:text-2xl lg:mx-0 lg:leading-relaxed">
                  Üye yönetiminden randevu sistemine, analitik raporlardan çoklu şube yönetimine kadar tüm ihtiyaçlarınızı karşılayan akıllı platform.
                </p>
              </AnimatedSection>

              <AnimatedSection animation="fade-up" delay={300}>
                <div className="mt-10 flex flex-col items-center gap-4 sm:flex-row sm:justify-center lg:justify-start">
                  <Button
                    asChild
                    size="lg"
                    className="group h-14 px-8 text-lg font-semibold shadow-lg transition-all duration-300 hover:shadow-xl"
                  >
                    <Link href="/onboarding">
                      Ücretsiz Başla
                      <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                    </Link>
                  </Button>
                  
                  <Button
                    asChild
                    variant="outline"
                    size="lg"
                    className="group h-14 px-8 text-lg font-semibold border-primary/20 hover:bg-primary/5"
                  >
                    <Link href="/features">
                      <Play className="mr-2 h-5 w-5" />
                      Demo İzle
                    </Link>
                  </Button>
                </div>

                {/* Quick benefits */}
                <div className="mt-10 grid grid-cols-1 gap-4 sm:grid-cols-3">
                  <div className="group flex items-center gap-3 rounded-xl border border-primary/20 bg-card/80 p-4 backdrop-blur transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
                    <div className="rounded-lg bg-green-500/10 p-2">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <span className="text-sm font-medium">
                      İlk Ay ücretsiz deneme
                    </span>
                  </div>
                  
                  <div className="group flex items-center gap-3 rounded-xl border border-primary/20 bg-card/80 p-4 backdrop-blur transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
                    <div className="rounded-lg bg-blue-500/10 p-2">
                      <Shield className="h-5 w-5 text-blue-600" />
                    </div>
                    <span className="text-sm font-medium">
                      Güvenli & KVKK uyumlu
                    </span>
                  </div>
                  
                  <div className="group flex items-center gap-3 rounded-xl border border-primary/20 bg-card/80 p-4 backdrop-blur transition-all duration-300 hover:-translate-y-1 hover:shadow-md">
                    <div className="rounded-lg bg-purple-500/10 p-2">
                      <Users className="h-5 w-5 text-purple-600" />
                    </div>
                    <span className="text-sm font-medium">
                      Sınırsız üye desteği
                    </span>
                  </div>
                </div>
              </AnimatedSection>
            </div>

            {/* Right Visual */}
            <AnimatedSection
              className="relative"
              animation="slide-left"
              delay={400}
            >
              <div className="relative mx-auto w-full max-w-2xl">
                {/* Floating decorative elements */}
                <div className="absolute -top-8 -right-8 h-24 w-24 rounded-full bg-gradient-to-br from-primary/30 to-secondary/20 blur-xl" />
                <div className="absolute -bottom-6 -left-6 h-20 w-20 rounded-full bg-gradient-to-br from-secondary/30 to-primary/20 blur-xl" />

                <div className="relative rounded-3xl border border-primary/20 bg-gradient-to-br from-card/95 to-card/80 p-8 shadow-2xl backdrop-blur-sm">
                  {/* Browser-like header */}
                  <div className="mb-6 flex items-center justify-between rounded-xl border border-border/50 bg-background/95 p-4">
                    <div className="flex items-center gap-2">
                      <div className="h-3 w-3 rounded-full bg-red-400" />
                      <div className="h-3 w-3 rounded-full bg-yellow-400" />
                      <div className="h-3 w-3 rounded-full bg-green-400" />
                    </div>
                    <div className="text-xs text-muted-foreground">
                      app.sportiva.com.tr
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs font-medium">4.9</span>
                    </div>
                  </div>

                  {/* Dashboard mockup */}
                  <div className="space-y-4">
                    {/* Stats cards */}
                    <div className="grid grid-cols-3 gap-3">
                      <div className="relative overflow-hidden rounded-lg border border-primary/20 bg-gradient-to-br from-primary/20 to-primary/10 p-4">
                        <div className="absolute inset-0 bg-primary/5" />
                        <div className="relative">
                          <Users className="h-5 w-5 text-primary mb-2" />
                          <p className="text-xs text-muted-foreground">Aktif Üye</p>
                          <p className="text-lg font-bold text-primary">2,847</p>
                        </div>
                      </div>
                      
                      <div className="relative overflow-hidden rounded-lg border border-green-200/50 bg-gradient-to-br from-green-500/20 to-green-500/10 p-4">
                        <div className="absolute inset-0 bg-green-500/5" />
                        <div className="relative">
                          <TrendingUp className="h-5 w-5 text-green-600 mb-2" />
                          <p className="text-xs text-muted-foreground">Bu Ay Gelir</p>
                          <p className="text-lg font-bold text-green-600">₺87K</p>
                        </div>
                      </div>
                      
                      <div className="relative overflow-hidden rounded-lg border border-blue-200/50 bg-gradient-to-br from-blue-500/20 to-blue-500/10 p-4">
                        <div className="absolute inset-0 bg-blue-500/5" />
                        <div className="relative">
                          <CheckCircle className="h-5 w-5 text-blue-600 mb-2" />
                          <p className="text-xs text-muted-foreground">Tamamlanan</p>
                          <p className="text-lg font-bold text-blue-600">1,243</p>
                        </div>
                      </div>
                    </div>

                    {/* Chart area */}
                    <div className="rounded-lg border border-border/50 bg-background/50 p-4">
                      <div className="mb-3 flex items-center justify-between">
                        <p className="text-sm font-medium">Üyelik Trendleri</p>
                        <Badge variant="secondary" className="text-xs">+24%</Badge>
                      </div>
                      <div className="h-20 rounded bg-gradient-to-r from-primary/20 via-primary/30 to-primary/10" />
                    </div>

                    {/* Action buttons */}
                    <div className="grid grid-cols-2 gap-3">
                      <Button variant="outline" size="sm" className="justify-start">
                        <Users className="mr-2 h-4 w-4" />
                        Yeni Üye Ekle
                      </Button>
                      <Button variant="outline" size="sm" className="justify-start">
                        <TrendingUp className="mr-2 h-4 w-4" />
                        Raporları Gör
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          </div>
        </div>
      </div>
    </section>
  );
}