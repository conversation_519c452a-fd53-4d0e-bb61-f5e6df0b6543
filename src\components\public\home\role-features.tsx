"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>bell, Crown, CheckCircle2, ArrowRight, Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";

export function RoleFeatures() {
  const roles = [
    {
      id: "member",
      title: "Ü<PERSON>",
      description:
        "Salonlara katıl, antrenmanlarını planla ve ilerlemeni takip et.",
      icon: Users,
      badge: { label: "Ücretsiz", variant: "secondary" as const },
      gradient: "from-blue-500/10 to-cyan-500/10",
      iconBg: "bg-blue-500/10",
      iconColor: "text-blue-600",
      borderColor: "hover:border-blue-200",
      features: [
        "Salon bul, filtrele (şehir, özellik, puan)",
        "Üyelik başvu<PERSON>u ve davet yönet<PERSON>i",
        "<PERSON><PERSON><PERSON> ve ders rezer<PERSON>yonu",
        "<PERSON><PERSON><PERSON><PERSON> ve hedef takibi",
        "<PERSON><PERSON><PERSON><PERSON>, ölçüm ve raporlar",
        "Paketleri görüntüleme ve yenileme",
        "Değerlendirme ve yorum bırakma",
        "Bildirim tercihleri (e‑posta/push)",
      ],
      cta: { label: "Üye olarak keşfet", href: "/features" },
    },
    {
      id: "trainer",
      title: "Antrenör",
      description:
        "Müşterilerini yönet, programlar oluştur ve gelirini takip et.",
      icon: Dumbbell,
      badge: { label: "Popüler", variant: "default" as const },
      gradient: "from-orange-500/10 to-red-500/10",
      iconBg: "bg-orange-500/10",
      iconColor: "text-orange-600",
      borderColor: "hover:border-orange-200",
      popular: true,
      features: [
        "Müşteri ve randevu yönetimi",
        "Kişiye özel program ve ders planları",
        "Çakışma önleme ve hatırlatmalar",
        "Paket/oturum bazlı gelir takibi",
        "Değerlendirme ve geri bildirim",
        "Salon(lar) ile çalışma ve davet/başvuru",
        "Haftalık/aylık performans istatistikleri",
        "İzin tabanlı işlem yetkileri",
      ],
      cta: { label: "Antrenör özellikleri", href: "/features" },
    },
    {
      id: 'manager',
      title: "Yönetici",
      description:
        "Şubenizi uçtan uca yönetin, ekip ve finansı tek panelden kontrol edin.",
      icon: Crown,
      badge: { label: "Premium", variant: "destructive" as const },
      gradient: "from-purple-500/10 to-pink-500/10",
      iconBg: "bg-purple-500/10",
      iconColor: "text-purple-600",
      borderColor: "hover:border-purple-200",
      features: [
        "Çoklu şube ve merkezi rol yönetimi",
        "Üyelik ve paket süreçleri (yönetimsel)",
        "Gelişmiş analitik (gelir, doluluk, performans)",
        "Randevu politikaları ve kaynak planlama",
        "Güvenlik, RLS ve KVKK uyumu",
        "İş zekâsı panoları ve dışa aktarma (CSV)",
        "Otomasyon ve bildirim akışları",
      ],
      cta: { label: "Yönetici özellikleri", href: "/features" },
    },
  ];

  return (
    <section className="relative py-20 lg:py-28 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/[0.02] via-transparent to-primary/[0.02]" />
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
      
      <div className="container relative mx-auto px-4">
        <div className="mx-auto mb-16 max-w-3xl text-center">
          <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6">
            <Sparkles className="h-4 w-4" />
            Roller
          </div>
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl mb-6">
            Her rol için
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent"> net faydalar</span>
          </h2>
          <p className="text-muted-foreground text-lg leading-relaxed">
            Üye, Antrenör ve Yönetici deneyimleri ayrı ayrı optimize edildi. İhtiyacınıza uygun
            akışları keşfedin ve potansiyelinizi açığa çıkarın.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {roles.map((role) => {
            const Icon = role.icon;
            return (
              <Card 
                key={role.id} 
                className={cn(
                  "group relative h-full overflow-hidden border-0 bg-white/50 backdrop-blur-sm transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl",
                  role.borderColor,
                  role.popular && "ring-2 ring-primary/20 shadow-lg scale-[1.02]"
                )}
                style={{
                  background: `linear-gradient(135deg, var(--card) 0%, var(--card) 100%), linear-gradient(135deg, ${role.gradient.split(' ')[1]} 0%, ${role.gradient.split(' ')[3]} 100%)`,
                  backgroundBlendMode: 'normal, multiply'
                }}
              >
                {/* Popular Badge */}
                {role.popular && (
                  <div className="absolute -top-2 -right-2 z-10">
                    <div className="bg-primary text-primary-foreground text-xs font-semibold px-3 py-1 rounded-full shadow-lg">
                      ⭐ En Popüler
                    </div>
                  </div>
                )}
                
                {/* Gradient Overlay */}
                <div className={cn("absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 bg-gradient-to-br", role.gradient)} />
                
                <CardHeader className="relative pb-4">
                  <div className="mb-4 flex items-start justify-between">
                    <div className="flex items-center gap-4">
                      <div className={cn(
                        "relative flex h-14 w-14 items-center justify-center rounded-2xl transition-all duration-300 group-hover:scale-110",
                        role.iconBg
                      )}>
                        <Icon className={cn("h-7 w-7 transition-colors duration-300", role.iconColor)} />
                        <div className="absolute inset-0 rounded-2xl bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold mb-1">{role.title}</CardTitle>
                        <Badge variant={role.badge.variant} className="text-xs">
                          {role.badge.label}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <p className="text-muted-foreground text-sm leading-relaxed">{role.description}</p>
                </CardHeader>
                
                <CardContent className="relative space-y-6">
                  <div className="space-y-3">
                    {role.features.slice(0, 6).map((feature, idx) => (
                      <div 
                        key={idx} 
                        className="flex items-start gap-3 text-sm opacity-90 hover:opacity-100 transition-opacity duration-200"
                        style={{ animationDelay: `${idx * 50}ms` }}
                      >
                        <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="leading-relaxed">{feature}</span>
                      </div>
                    ))}
                    {role.features.length > 6 && (
                      <div className="text-xs text-muted-foreground font-medium pt-2">
                        +{role.features.length - 6} özellik daha
                      </div>
                    )}
                  </div>
                  
                  <Button 
                    asChild 
                    className={cn(
                      "w-full group/btn transition-all duration-300 bg-white/80 hover:bg-white text-foreground border border-border/50 hover:border-primary/50 hover:shadow-lg",
                      role.popular && "bg-primary hover:bg-primary/90 text-primary-foreground border-primary"
                    )}
                  >
                    <Link href={role.cta.href} className="flex items-center justify-center gap-2">
                      {role.cta.label}
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover/btn:translate-x-1" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        {/* Bottom CTA */}
        <div className="mt-16 text-center">
          <p className="text-muted-foreground text-sm mb-4">
            Hangi rolün size uygun olduğundan emin değil misiniz?
          </p>
          <Button variant="outline" asChild className="group">
            <Link href="/features" className="flex items-center gap-2">
              Tüm özellikleri karşılaştır
              <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}


