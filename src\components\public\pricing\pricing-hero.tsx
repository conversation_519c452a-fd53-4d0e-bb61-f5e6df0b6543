"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Zap, ArrowDown, Star, Users, Trophy, Shield, Clock } from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";

// Static data moved outside component to prevent recreation
const testimonials = [
  { text: "Sportiva sayesinde salon yönetimi çok kolaylaştı!", author: "Ahmet K.", role: "Salon Sahibi" },
  { text: "Üye sayımız %40 arttı, gelirlerimiz ikiye katlandı.", author: "Zeynep M.", role: "Fitness Merkezi" },
  { text: "Dijitalleşme sürecimiz hiç bu kadar kolay olmamıştı.", author: "Murat D.", role: "Spor Kompleksi" }
];

export function PricingHero() {
  const [isVisible, setIsVisible] = useState(false);
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []); // testimonials is static, no need to add as dependency

  const benefits = [
    { icon: Shield, text: "Güvenli altyapı", desc: "SSL şifreleme" },
    { icon: Clock, text: "Hızlı kurulum", desc: "24 saat içinde" },
    { icon: Users, text: "Uzman destek", desc: "7/24 Türkçe" },
    { icon: Trophy, text: "Kanıtlanmış başarı", desc: "500+ salon" }
  ];

  const stats = [
    { number: "500+", label: "Aktif Salon" },
    { number: "50K+", label: "Mutlu Üye" },
    { number: "99.9%", label: "Uptime" },
    { number: "24/7", label: "Destek" }
  ];

  return (
    <section className="relative py-20 lg:py-32 bg-gradient-to-br from-background via-background to-primary/5 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Top badges with animation */}
          <div className={`flex flex-col sm:flex-row gap-3 justify-center items-center mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors">
              <Zap className="h-4 w-4 mr-2" />
              Fiyatlandırma
            </Badge>
            <Badge variant="outline" className="px-3 py-1 text-xs font-medium bg-success/10 text-success border-success/20">
              <Star className="h-3 w-3 mr-1 fill-current" />
              4.9/5 Müşteri Memnuniyeti
            </Badge>
          </div>

          <div className="text-center mb-16">
            {/* Main Heading with staggered animation */}
            <h1 className={`text-4xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6 transition-all duration-1000 delay-200 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <span className="text-foreground">İşletmenize</span>
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary via-primary to-blue-600 animate-gradient">
                Mükemmel Plan
              </span>
            </h1>

            <p className={`text-xl md:text-2xl text-muted-foreground leading-relaxed mb-8 max-w-4xl mx-auto transition-all duration-1000 delay-400 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              Spor salonunuzun büyüklüğüne göre özel tasarlanmış esnek fiyatlandırma.
              <br className="hidden md:block" />
              <span className="text-primary font-semibold">14 gün ücretsiz deneme</span> ile hemen başlayın.
            </p>

            {/* Rotating testimonials */}
            <div className={`mb-12 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <div className="bg-card/60 backdrop-blur-sm border border-border/20 rounded-2xl p-6 max-w-2xl mx-auto shadow-lg">
                <div className="transition-all duration-500">
                  <p className="text-lg font-medium text-foreground mb-2 italic">
                  &quot;{testimonials[currentTestimonial].text}&quot;
                  </p>
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <Users className="h-4 w-4 text-primary" />
                    </div>
                    <span className="font-semibold text-primary">{testimonials[currentTestimonial].author}</span>
                    <span className="text-muted-foreground">• {testimonials[currentTestimonial].role}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className={`flex flex-col sm:flex-row gap-4 justify-center mb-16 transition-all duration-1000 delay-700 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <Button asChild size="lg" className="bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all">
                <Link href="/auth/register">
                  <Trophy className="mr-2 h-5 w-5" />
                  Ücretsiz Başla - 14 Gün
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="bg-card/50 backdrop-blur-sm border-border/30 hover:bg-card/70">
                <Link href="#pricing-plans">
                  Planları İncele
                  <ArrowDown className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className={`grid grid-cols-2 md:grid-cols-4 gap-8 mb-16 transition-all duration-1000 delay-800 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="bg-card/60 backdrop-blur-sm border border-border/20 rounded-xl p-6 hover:bg-card/70 transition-all group-hover:scale-105 shadow-lg">
                  <div className="text-3xl md:text-4xl font-bold text-primary mb-2">{stat.number}</div>
                  <div className="text-sm text-muted-foreground font-medium">{stat.label}</div>
                </div>
              </div>
            ))}
          </div>

          {/* Benefits Grid */}
          <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12 transition-all duration-1000 delay-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            {benefits.map((benefit, index) => {
              const IconComponent = benefit.icon;
              return (
                <div key={index} className="group">
                  <div className="bg-card/60 backdrop-blur-sm border border-border/20 rounded-xl p-6 text-center hover:bg-card/70 transition-all group-hover:scale-105 shadow-lg">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-primary/20 transition-colors">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="font-semibold text-foreground mb-2">{benefit.text}</h3>
                    <p className="text-sm text-muted-foreground">{benefit.desc}</p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Scroll Indicator */}
          <div className="text-center">
            <Link
              href="#pricing-plans"
              className="inline-flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors group"
              aria-label="Fiyatlandırma planlarına kaydır"
            >
              <span className="text-sm font-medium">Planları detaylı inceleyin</span>
              <ArrowDown className="h-4 w-4 group-hover:translate-y-1 transition-transform animate-bounce" />
            </Link>
          </div>
        </div>
      </div>

      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-primary/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/10 to-primary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-gradient-to-r from-primary/5 to-blue-500/5 rounded-full blur-3xl"></div>
        
        {/* Floating elements */}
        <div className="absolute top-20 right-20 w-4 h-4 bg-primary/20 rounded-full animate-float"></div>
        <div className="absolute bottom-32 left-16 w-6 h-6 bg-blue-500/20 rounded-full animate-float delay-500"></div>
        <div className="absolute top-1/3 right-1/3 w-8 h-8 bg-primary/10 rounded-full animate-float delay-1000"></div>
      </div>

      <style jsx>{`
        @keyframes gradient {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        .animate-gradient {
          background-size: 200% 200%;
          animation: gradient 3s ease infinite;
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </section>
  );
}
