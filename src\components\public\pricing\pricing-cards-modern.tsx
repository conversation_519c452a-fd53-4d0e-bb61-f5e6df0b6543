"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, Crown, Zap, Star, ArrowRight, Sparkles, Shield, HeartHandshake } from "lucide-react";
import Link from "next/link";
import type { PlatformPackages } from "@/types/database/tables";
import { useState, useEffect } from "react";

type PricingCardsModernProps = {
  packages: PlatformPackages[];
};

export function PricingCardsModern({ packages }: PricingCardsModernProps) {
  const [isYearly, setIsYearly] = useState(true);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Helper functions
  const formatFeatures = (features: unknown): string[] => {
    if (Array.isArray(features)) {
      return features.filter((item): item is string => typeof item === "string");
    }
    if (typeof features === "string") {
      try {
        const parsed = JSON.parse(features);
        return formatFeatures(parsed);
      } catch {
        return [];
      }
    }
    if (typeof features === "object" && features !== null) {
      const obj = features as Record<string, unknown>;
      if (Array.isArray(obj.perks)) {
        return (obj.perks as unknown[]).filter((v): v is string => typeof v === "string");
      }
      const directStrings = Object.values(obj).filter((v): v is string => typeof v === "string");
      if (directStrings.length) return directStrings;
      const nested = Object.values(obj)
        .flatMap((v) => (Array.isArray(v) ? v : []))
        .filter((v): v is string => typeof v === "string");
      return nested;
    }
    return [];
  };

  const prettifyFeature = (text: string): string => {
    try {
      let t = (text || "").toString().trim();
      if (!t) return t;
      t = t.replace(/^Maks\s/i, "Maksimum ");
      t = t.replace(/\bmax\b/i, "Maksimum");
      t = t.replace(/uye/gi, "üye");
      t = t.charAt(0).toUpperCase() + t.slice(1);
      return t;
    } catch {
      return text as string;
    }
  };

  const groupedPackages = packages.reduce(
    (acc, pkg) => {
      if (!acc[pkg.tier]) acc[pkg.tier] = [];
      acc[pkg.tier].push(pkg);
      return acc;
    },
    {} as Record<string, PlatformPackages[]>,
  );

  const tierOrder = ["free", "starter", "professional"];
  const tiersSorted = Object.keys(groupedPackages).sort((a, b) => 
    (tierOrder.indexOf(a) !== -1 ? tierOrder.indexOf(a) : 99) - 
    (tierOrder.indexOf(b) !== -1 ? tierOrder.indexOf(b) : 99)
  );

  const tl = (n: number) => n.toLocaleString("tr-TR");

  // Build auto features from table columns
  const buildAutoFeatures = (pkg: PlatformPackages): string[] => {
    const items: string[] = [];
    const num = (v?: number | null) => (typeof v === "number" && Number.isFinite(v) ? v : null);
    const label = (count: number | null, finiteText: string, unlimitedText: string) =>
      count === null ? unlimitedText : `${tl(count)} ${finiteText}`;

    items.push(label(num(pkg.max_gyms), "spor salonu", "Sınırsız spor salonu"));
    items.push(label(num(pkg.max_members), "üye kapasitesi", "Sınırsız üye"));
    items.push(label(num(pkg.max_trainers), "antrenör", "Sınırsız antrenör"));
    items.push(label(num(pkg.max_staff), "personel", "Sınırsız personel"));
    items.push(label(num(pkg.max_monthly_appointments), "aylık randevu", "Sınırsız randevu"));

    return items;
  };

  const getMonthlyPrice = (yearlyPrice: number) => Math.round(yearlyPrice / 10);

  // Tier configurations
  const tierConfig = {
    free: {
      name: "Başlangıç",
      icon: HeartHandshake,
      gradient: "from-gray-500 to-gray-600",
      popular: false,
      description: "Küçük salonlar için ideal başlangıç paketi"
    },
    starter: {
      name: "Gelişim", 
      icon: Star,
      gradient: "from-blue-500 to-blue-600",
      popular: true,
      description: "Büyüyen işletmeler için en popüler seçim"
    },
    professional: {
      name: "Kurumsal",
      icon: Crown,
      gradient: "from-purple-500 to-purple-600",
      popular: false,
      description: "Büyük işletmeler için profesyonel çözüm"
    }
  };

  return (
    <section id="pricing-plans" className="py-20 lg:py-32 bg-gradient-to-b from-background to-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className={`text-center mb-16 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="inline-flex items-center gap-2 rounded-full border border-primary/30 bg-primary/10 px-4 py-2 text-sm font-medium text-primary mb-6">
              <Sparkles className="h-4 w-4" />
              <span>Fiyatlandırma Planları</span>
            </div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
              İhtiyacınıza Göre <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-blue-600">Mükemmel Plan</span>
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-8">
              Spor salonunuzun büyüklüğüne ve hedeflerinize uygun esnek fiyatlandırma seçenekleri.
              Tüm planlar 14 gün ücretsiz deneme ile başlar.
            </p>

            {/* Monthly/Yearly Toggle */}
            <div className="inline-flex items-center gap-3 bg-card/60 backdrop-blur-sm border border-border/20 rounded-full p-2">
              <span className={`text-sm font-medium px-3 py-1 rounded-full transition-all ${!isYearly ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'}`}>
                Aylık
              </span>
              <Switch
                checked={isYearly}
                onCheckedChange={setIsYearly}
                className="data-[state=checked]:bg-primary"
              />
              <span className={`text-sm font-medium px-3 py-1 rounded-full transition-all ${isYearly ? 'bg-primary text-primary-foreground' : 'text-muted-foreground'}`}>
                Yıllık
              </span>
              {isYearly && (
                <Badge variant="secondary" className="bg-success/10 text-success border-success/20">
                  %65 İndirim
                </Badge>
              )}
            </div>
          </div>

          {/* Pricing Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {tiersSorted.map((tier, index) => {
              const tierPackages = groupedPackages[tier] ?? [];
              const isLifetime = (d: string | null | undefined) =>
                d == null || d === "lifetime" || d === "free" || d === "";
              const yearlyPkg = tierPackages.find((p) => p.duration === "yearly");
              const lifetimePkg = tierPackages.find((p) => isLifetime(p.duration as any));
              
              if (!yearlyPkg && !lifetimePkg && tierPackages.length === 0) return null;

              const currentPkg = lifetimePkg ?? yearlyPkg ?? tierPackages[0];
              const explicitFeatures = formatFeatures(currentPkg.features);
              const auto = buildAutoFeatures(currentPkg);
              const mergedSet = new Set<string>([...explicitFeatures, ...auto]);
              const features = Array.from(mergedSet).slice(0, 8);
              
              const config = tierConfig[tier as keyof typeof tierConfig];
              if (!config) return null;

              const IconComponent = config.icon;
              const yearlyPrice = currentPkg.price;
              const monthlyPrice = getMonthlyPrice(yearlyPrice);
              const displayPrice = isYearly ? yearlyPrice : monthlyPrice;
              const priceLabel = isYearly ? "/yıl" : "/ay";
              const oldMonthlyPrice = Math.round(monthlyPrice / 0.35); // For showing discount

              const isHovered = hoveredCard === tier;

              return (
                <div
                  key={tier}
                  className={`transition-all duration-500 delay-${index * 100} ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
                  onMouseEnter={() => setHoveredCard(tier)}
                  onMouseLeave={() => setHoveredCard(null)}
                >
                  <Card className={`relative h-full border-border ${config.popular ? 'border-2 border-primary shadow-2xl scale-105' : 'border shadow-lg'} 
                    ${isHovered ? 'shadow-2xl scale-105' : ''} transition-all duration-300 bg-card overflow-hidden`}>
                    
                    {/* Popular Badge */}
                    {config.popular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-gradient-to-r from-primary to-blue-600 text-white px-4 py-1 shadow-lg">
                          <Star className="h-3 w-3 mr-1 fill-current" />
                          En Popüler
                        </Badge>
                      </div>
                    )}

                    {/* Floating Icon */}
                    <div className="absolute top-4 right-4">
                      <div className={`w-12 h-12 bg-gradient-to-r ${config.gradient} rounded-full flex items-center justify-center shadow-lg ${isHovered ? 'scale-110' : ''} transition-transform`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                    </div>

                    <CardHeader className="pt-8 pb-4">
                      <CardTitle className="text-2xl font-bold text-foreground mb-2">
                        {config.name}
                      </CardTitle>
                      <CardDescription className="text-muted-foreground mb-4">
                        {config.description}
                      </CardDescription>
                      
                      {/* Price Display */}
                      <div className="mb-4">
                        {tier === "free" ? (
                          <div className="text-4xl font-bold text-foreground">
                            Ücretsiz
                          </div>
                        ) : (
                          <div className="space-y-2">
                            <div className="flex items-baseline gap-2">
                              <span className="text-4xl font-bold text-foreground">
                                ₺{tl(displayPrice)}
                              </span>
                              <span className="text-muted-foreground">{priceLabel}</span>
                            </div>
                            {isYearly && (
                              <div className="text-sm text-muted-foreground">
                                <span className="line-through">₺{tl(oldMonthlyPrice)}/ay</span>
                                <span className="text-green-600 font-medium ml-2">%65 tasarruf</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </CardHeader>

                    <CardContent className="flex-1">
                      <ul className="space-y-3">
                        {features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-3">
                            <Check className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                            <span className="text-sm text-foreground">{prettifyFeature(feature)}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>

                    <CardFooter className="pt-6">
                      <Button 
                        asChild 
                        className={`w-full ${config.popular ? 'bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 text-white shadow-lg' : 'bg-background border hover:bg-muted'} 
                          ${isHovered ? 'shadow-xl scale-105' : ''} transition-all`}
                        size="lg"
                      >
                        <Link href="/auth/register">
                          {tier === "free" ? "Ücretsiz Başla" : "Plan Seç"}
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardFooter>

                    {/* Hover Effect Background */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${config.gradient} opacity-0 ${isHovered ? 'opacity-5' : ''} transition-opacity pointer-events-none`} />
                  </Card>
                </div>
              );
            })}
          </div>

          {/* Bottom Info */}
          <div className={`text-center mt-16 transition-all duration-1000 delay-600 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
            <div className="bg-card/60 backdrop-blur-sm border border-border/20 rounded-2xl p-6 max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center justify-center gap-3">
                  <Shield className="h-6 w-6 text-primary" />
                  <div className="text-left">
                    <div className="font-semibold text-foreground">Güvenli Ödeme</div>
                    <div className="text-sm text-muted-foreground">SSL şifreleme ile korunur</div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-3">
                  <Zap className="h-6 w-6 text-primary" />
                  <div className="text-left">
                    <div className="font-semibold text-foreground">Anında Aktivasyon</div>
                    <div className="text-sm text-muted-foreground">Hemen kullanmaya başlayın</div>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-3">
                  <HeartHandshake className="h-6 w-6 text-primary" />
                  <div className="text-left">
                    <div className="font-semibold text-foreground">İstediğiniz Zaman İptal</div>
                    <div className="text-sm text-muted-foreground">Taahhüt yok, özgürce iptal</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}