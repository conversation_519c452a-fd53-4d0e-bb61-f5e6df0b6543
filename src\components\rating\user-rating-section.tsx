'use client';

import { useState, useEffect, useTransition } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { StarRating } from '@/components/ui/star-rating';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { RatingDialog } from './rating-dialog';
import { 
  Star, 
  Edit3, 
  Trash2, 
  MessageCircle, 
  AlertTriangle,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  UserRatingSectionProps, 
  UserRating,
  ExistingRatingCardProps 
} from '@/types/features/rating';
import { 
  canUserRate, 
  formatRatingDate, 
  getRatingSuccessMessage 
} from '@/lib/utils/rating-utils';
import { 
  getUserGymRating, 
  deleteGymRating 
} from '@/lib/actions/rating/rating-actions';

export function UserRatingSection({
  gymId,
  userId,
  membershipStatus,
  onRatingUpdate,
}: UserRatingSectionProps) {
  const [userRating, setUserRating] = useState<UserRating | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isPending, startTransition] = useTransition();

  // Check if user can rate
  const ratingEligibility = canUserRate(membershipStatus, userRating || undefined);

  // Fetch user's existing rating
  useEffect(() => {
    if (!userId) {
      setIsLoading(false);
      return;
    }

    const fetchUserRating = async () => {
      try {
        const response = await getUserGymRating(gymId);
        if (response.success) {
          setUserRating(response.data || null);
        }
      } catch (error) {
        console.error('Error fetching user rating:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserRating();
  }, [gymId, userId]);

  const handleRatingSubmit = (rating: UserRating) => {
    setUserRating(rating);
    onRatingUpdate?.();
  };

  const handleEditRating = () => {
    setIsDialogOpen(true);
  };

  const handleDeleteRating = async () => {
    if (!userRating) {
      return;
    }

    setIsConfirmDeleteOpen(true);
  };

  const confirmDeleteRating = async () => {
    if (!userRating) {
      return;
    }

    startTransition(async () => {
      try {
        const response = await deleteGymRating({ ratingId: userRating.id });
        
        if (response.success) {
          toast.success(getRatingSuccessMessage('delete'));
          setUserRating(null);
          onRatingUpdate?.();
        } else {
          throw new Error(response.error || 'Silme işlemi başarısız');
        }
      } catch (error) {
        console.error('Delete rating error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Değerlendirme silinirken bir hata oluştu';
        toast.error(errorMessage);
      }
    });
  };

  // Don't show anything for non-authenticated users
  if (!userId) {
    return null;
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-pulse flex space-x-2">
          <div className="h-4 w-4 bg-muted rounded-full"></div>
          <div className="h-4 w-4 bg-muted rounded-full"></div>
          <div className="h-4 w-4 bg-muted rounded-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {userRating ? (
        <ExistingRatingCard
          rating={userRating}
          onEdit={handleEditRating}
          onDelete={handleDeleteRating}
          isDeleting={isPending}
        />
      ) : (
        <AddRatingCard
          canRate={ratingEligibility.canRate}
          reason={ratingEligibility.reason}
          onRate={() => setIsDialogOpen(true)}
        />
      )}

      <RatingDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        gymId={gymId}
        onRatingSubmit={handleRatingSubmit}
        existingRating={userRating || undefined}
      />

      <ConfirmationDialog
        isOpen={isConfirmDeleteOpen}
        onOpenChange={setIsConfirmDeleteOpen}
        title="Değerlendirmeyi Sil"
        description="Değerlendirmenizi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."
        confirmText="Sil"
        cancelText="İptal"
        onConfirm={confirmDeleteRating}
        variant="destructive"
        isLoading={isPending}
      />
    </div>
  );
}

function ExistingRatingCard({ 
  rating, 
  onEdit, 
  onDelete, 
  isDeleting 
}: ExistingRatingCardProps & { isDeleting?: boolean }) {
  return (
    <Card className="from-primary/5 to-primary/10 border-primary/20 bg-gradient-to-r p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-3">
          <div className="flex items-center gap-2">
            <Star className="text-primary h-5 w-5" />
            <span className="text-foreground font-semibold">
              Değerlendirmeniz
            </span>
          </div>

          <div className="flex items-center gap-3">
            <StarRating rating={rating.rating} readonly size="md" />
            <span className="text-muted-foreground text-sm">
              {formatRatingDate(rating.created_at)}
            </span>
          </div>

          {rating.comment && (
            <div className="space-y-2">
              <div className="text-muted-foreground flex items-center gap-2 text-sm">
                <MessageCircle className="h-4 w-4" />
                <span>Yorumunuz</span>
              </div>
              <p className="text-foreground bg-background/50 rounded-lg p-3 text-sm">
                &quot;{rating.comment}&quot;
              </p>
            </div>
          )}
        </div>

        <div className="ml-4 flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onEdit}
            className="text-primary border-primary/20 hover:bg-primary/10"
          >
            <Edit3 className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onDelete}
            disabled={isDeleting}
            className="text-destructive border-destructive/20 hover:bg-destructive/10"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
}

function AddRatingCard({ 
  canRate, 
  reason, 
  onRate 
}: { 
  canRate: boolean; 
  reason?: string; 
  onRate: () => void; 
}) {
  if (!canRate) {
    return (
      <Card className="p-6 bg-muted/50 border-muted">
        <div className="flex items-center gap-3 text-muted-foreground">
          <AlertTriangle className="h-5 w-5" />
          <span className="text-sm">{reason}</span>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 border-2 border-dashed border-primary/30 hover:border-primary/50 transition-colors">
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="p-3 bg-primary/10 rounded-full">
            <Star className="h-6 w-6 text-primary" />
          </div>
        </div>
        
        <div className="space-y-2">
          <h3 className="font-semibold text-foreground">Bu salonu değerlendirin</h3>
          <p className="text-sm text-muted-foreground">
            Deneyiminizi paylaşın ve diğer üyelere yardımcı olun
          </p>
        </div>

        <Button onClick={onRate} className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          Değerlendirme Yap
        </Button>
      </div>
    </Card>
  );
}