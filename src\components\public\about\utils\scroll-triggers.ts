"use client";

import { useEffect, useRef, useCallback } from "react";

interface UseScrollTriggerOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  enabled?: boolean;
}

interface ScrollTriggerReturn {
  ref: React.RefObject<HTMLElement | null>;
  isIntersecting: boolean;
  hasTriggered: boolean;
}

export function useScrollTrigger({
  threshold = 0.1,
  rootMargin = "0px 0px -10% 0px",
  triggerOnce = true,
  enabled = true
}: UseScrollTriggerOptions = {}): ScrollTriggerReturn {
  const ref = useRef<HTMLElement>(null);
  const isIntersectingRef = useRef(false);
  const hasTriggeredRef = useRef(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const setIntersecting = useCallback((value: boolean) => {
    isIntersectingRef.current = value;
    if (value) {
      hasTriggeredRef.current = true;
    }
  }, []);

  useEffect(() => {
    if (!enabled || !ref.current) return;

    const element = ref.current;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isIntersecting = entry.isIntersecting;
        
        if (isIntersecting) {
          setIntersecting(true);
          
          // Add data attribute for CSS animations
          element.setAttribute('data-intersecting', 'true');
          
          if (triggerOnce) {
            observer.unobserve(element);
          }
        } else if (!triggerOnce) {
          setIntersecting(false);
          element.setAttribute('data-intersecting', 'false');
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    observer.observe(element);
    observerRef.current = observer;

    return () => {
      observer.disconnect();
    };
  }, [enabled, threshold, rootMargin, triggerOnce, setIntersecting]);

  return {
    ref,
    isIntersecting: isIntersectingRef.current,
    hasTriggered: hasTriggeredRef.current
  };
}

// Hook for multiple scroll triggers
export function useMultipleScrollTriggers(
  count: number,
  options?: UseScrollTriggerOptions
) {
  const triggers = [];
  
  for (let i = 0; i < count; i++) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    triggers.push(useScrollTrigger(options));
  }
  
  return triggers;
}

// Hook for staggered animations
export function useStaggeredScrollTrigger(
  count: number,
  staggerDelay: number = 100,
  options?: UseScrollTriggerOptions
) {
  const triggers = useMultipleScrollTriggers(count, options);
  
  const getStaggerDelay = useCallback((index: number) => {
    return index * staggerDelay;
  }, [staggerDelay]);
  
  return {
    triggers,
    getStaggerDelay
  };
}

// Custom hook for scroll progress
export function useScrollProgress() {
  const progressRef = useRef(0);
  
  useEffect(() => {
    const updateScrollProgress = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = scrollTop / docHeight;
      progressRef.current = Math.min(Math.max(progress, 0), 1);
    };
    
    const throttledUpdate = throttle(updateScrollProgress, 16); // ~60fps
    
    window.addEventListener('scroll', throttledUpdate, { passive: true });
    updateScrollProgress();
    
    return () => {
      window.removeEventListener('scroll', throttledUpdate);
    };
  }, []);
  
  return progressRef.current;
}

// Throttle function for better performance
function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): T {
  let inThrottle: boolean;
  
  return ((...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }) as T;
}

// Utility for creating CSS classes with scroll triggers
export function createScrollTriggerClasses(
  baseClasses: string,
  animationClasses: string
): string {
  return `${baseClasses} transition-all duration-700 ease-out ${animationClasses}`;
}

// Prebuilt animation classes
export const scrollAnimationClasses = {
  fadeIn: "opacity-0 data-[intersecting=true]:opacity-100",
  slideUp: "opacity-0 translate-y-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-y-0",
  slideDown: "opacity-0 -translate-y-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-y-0", 
  slideLeft: "opacity-0 translate-x-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-x-0",
  slideRight: "opacity-0 -translate-x-8 data-[intersecting=true]:opacity-100 data-[intersecting=true]:translate-x-0",
  scale: "opacity-0 scale-95 data-[intersecting=true]:opacity-100 data-[intersecting=true]:scale-100",
  scaleDown: "opacity-0 scale-105 data-[intersecting=true]:opacity-100 data-[intersecting=true]:scale-100"
};